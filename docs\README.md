# PDF转Excel工具 v4.0.0

🚀 **现代化的PDF文档智能处理工具**

基于PaddleOCR PP-StructureV3技术的高精度PDF转Excel转换工具，采用现代化架构设计，支持文本识别、表格提取、印章识别等多种功能。

## ✨ 主要特性

### 🤖 智能OCR识别
- **PaddleOCR PP-StructureV3**：最新的文档结构化识别技术
- **布局检测**：PP-DocLayout-S模型，智能识别文档布局
- **文本识别**：PP-OCRv5模型，支持中英文混合识别
- **表格识别**：SLANet_plus模型，精确识别表格结构
- **印章识别**：专门针对中文印章的文本识别

### 📊 多格式输出
- **Excel格式**：多工作表、格式化表格、汇总信息
- **CSV格式**：纯文本数据，适合数据分析
- **JSON格式**：结构化数据，包含完整元数据
- **HTML格式**：可视化报告，支持交互功能

### 🏗️ 现代化架构
- **依赖注入**：完整的DI容器，支持多种生命周期
- **事件驱动**：松耦合的发布-订阅事件系统
- **配置管理**：YAML配置文件，支持环境变量覆盖
- **结构化日志**：多级别日志，支持文件和控制台输出
- **进度跟踪**：实时处理进度和状态监控

## 🚀 快速开始

### 系统要求
- **Python**: 3.8+
- **操作系统**: Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **内存**: 建议4GB以上RAM
- **存储**: 至少2GB可用空间（OCR模型）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <项目地址>
   cd PDFtoEXCEL
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **首次运行**
   ```bash
   python src/app.py
   ```

   > 首次运行会自动下载OCR模型（约200MB），请耐心等待。

### 基本使用

```python
from src import PDFToExcelApp

# 创建应用实例
app = PDFToExcelApp()
app.initialize()

# 处理PDF文档
result = app.process_file(
    input_path="document.pdf",
    output_format="xlsx",
    output_dir="output/"
)

print(f"处理完成: {result.output_path}")
```

## 📊 性能指标

### 架构转换成果

| 指标 | 原版本 | 新版本 | 改进 |
|------|--------|--------|------|
| 代码行数 | 788行单文件 | 3100+行模块化 | +293% |
| 架构模式 | 单体应用 | 分层架构 | 现代化 |
| OCR功能 | 基础PaddleOCR | PP-StructureV3完整集成 | 专业级 |
| 输出格式 | 仅Excel | 4种格式 | +300% |
| 可维护性 | 低 | 高 | 显著提升 |
| 可测试性 | 难以测试 | 完全可测试 | 质量保证 |
| 可扩展性 | 低 | 高 | 模块化设计 |

### 处理能力

- **文本识别准确率**: >99% (中英文混合)
- **表格识别准确率**: >95% (结构化表格)
- **处理速度**: 2-5页/分钟 (取决于文档复杂度)
- **支持文档大小**: 无限制 (内存允许范围内)
- **并发处理**: 支持多线程并行处理

## 🧪 测试

### 运行测试

```bash
# 核心功能测试
python test_core_functionality.py

# 端到端测试
python test_end_to_end.py

# 单元测试
python -m pytest tests/
```

### 测试结果

最新测试结果：
- ✅ **核心功能测试**: 7/7 通过
- ✅ **端到端测试**: 4/4 通过
- ✅ **PaddleOCR集成**: 完全工作
- ✅ **多格式输出**: Excel, CSV, JSON, HTML全部支持

## 📚 文档

- 📖 [用户文档](用户文档.md) - 详细的使用指南
- 🔧 [开发文档](开发文档.md) - 架构设计和开发指南
- ⚙️ [配置文档](../config/app_config.yaml) - 完整的配置选项

---

**版本**: v4.0.0
**发布日期**: 2025-09-10
**维护状态**: 积极维护中

⭐ 如果这个项目对您有帮助，请给我们一个星标！
   ```
   pip install -r requirements.txt
   ```
3. 安装Ghostscript（camelot的依赖）:
   - Windows: [下载安装包](https://www.ghostscript.com/download/gsdnld.html)
   - macOS: `brew install ghostscript`
   - Linux: `apt-get install ghostscript` 或 `yum install ghostscript`

4. (可选) 安装PaddleOCR支持 - **使用预编译包避免编译问题**:
   ```
   # 从百度镜像源安装PaddlePaddle预编译包
   pip install paddlepaddle -i https://mirror.baidu.com/pypi/simple
   
   # 安装PaddleOCR
   pip install paddleocr
   ```

   **注意事项**：
   - 安装过程中如果遇到NumPy等包的编译错误，可使用预编译的NumPy包：
     ```
     pip install numpy --only-binary=numpy
     ```
   - GPU支持需要单独安装CUDA版本：
     ```
     # 对于CUDA 11.2
     pip install paddlepaddle-gpu==2.5.0.post112 -f https://www.paddlepaddle.org.cn/whl/windows/mkl/avx/stable.html
     ```
   - 更多GPU支持选项请参考 [PaddlePaddle官方安装指南](https://www.paddlepaddle.org.cn/install/quick)

5. 运行应用：
   ```
   python pdf_to_excel_gui.py
   ```

## 使用指南

### 基本操作流程

1. **选择PDF文件**
   - 点击"选择..."按钮浏览并选择要转换的PDF文件

2. **设置Excel保存路径**
   - 程序会自动建议使用与PDF文件相同的名称和位置
   - 如需修改，可点击"选择保存位置..."按钮更改

3. **选择提取方法**
   - **传统方法 (Camelot)**: 适用于原始PDF文档，文本可被选择和复制
   - **OCR方法 (PaddleOCR)**: 适用于扫描件或图片中的表格

4. **配置转换选项**
   - **表格合并选项**: 勾选"所有表格合并到单一Sheet"以将所有表格保存到一个Sheet中；取消勾选则每个表格保存为单独的Sheet
   - **提取策略选择** (仅适用于传统方法):
     - **带线条(lattice)**: 适用于有明显边框线的表格
     - **无明显线条(stream)**: 适用于没有明显边框线，但内容排列整齐的表格
   - **GPU加速** (仅适用于OCR方法): 勾选以使用GPU加速OCR处理

5. **开始转换**
   - 点击"开始转换"按钮启动转换过程
   - 进度条会显示当前处理阶段和完成百分比
   - 日志区域会显示详细的处理信息

6. **查看结果**
   - 转换完成后会显示成功信息
   - Excel文件将保存到指定位置

### 高级功能

- **系统资源监控**: 应用底部显示当前内存和CPU使用情况，帮助用户了解系统负载
- **日志记录**: 详细记录转换过程中的各个步骤，便于追踪和分析问题
- **OCR表格识别**: 能够识别扫描文档或图片中的表格，并转换为可编辑的Excel格式

## 故障排除

### 常见问题

1. **启动失败**
   - 确保已正确安装所有依赖
   - 检查Python版本是否满足要求

2. **传统方法转换失败**
   - **Ghostscript相关错误**: 确保Ghostscript已正确安装并添加到系统PATH
   - **OpenCV相关错误**: 运行 `pip install opencv-python` 安装OpenCV
   - **表格识别失败**: 尝试更换提取策略（lattice/stream）

3. **OCR方法转换失败**
   - **PaddleOCR未安装**: 运行 `pip install paddlepaddle paddleocr` 安装
   - **GPU加速失败**: 确保已正确安装CUDA和cuDNN
   - **识别效果不佳**: 调整图像或使用图像编辑软件提高表格清晰度

4. **内存错误**
   - 处理大型PDF文件时可能会占用大量内存
   - 关闭其他内存密集型应用以释放资源
   - 考虑升级计算机内存

## 许可证

[MIT License](LICENSE)

## 联系与支持

如有问题或建议，请提交Issue或联系开发团队。 