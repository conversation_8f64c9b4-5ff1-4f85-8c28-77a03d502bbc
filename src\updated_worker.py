import sys
import os
from PySide6.QtCore import QObject, Signal

# PaddleOCR导入与错误处理
PADDLE_AVAILABLE = False
PADDLE_ERROR_MSG = ""
try:
    from paddleocr import PPStructure, save_structure_res
    PADDLE_AVAILABLE = True
except ImportError as e:
    PADDLE_ERROR_MSG = str(e)
except Exception as e:
    PADDLE_ERROR_MSG = f"加载PaddleOCR时出错: {str(e)}"

# --- PaddleOCR表格识别工作线程 ---
class PaddleOCRWorker(QObject):
    finished = Signal(bool, str)
    progress_log = Signal(str)
    progress_value = Signal(int)
    
    def __init__(self, pdf_path, excel_path, single_sheet, use_gpu=False):
        super().__init__()
        self.pdf_path = pdf_path
        self.excel_path = excel_path
        self.single_sheet = single_sheet
        self.use_gpu = use_gpu
        self.should_stop = False
    
    def run(self):
        if not PADDLE_AVAILABLE:
            error_msg = f"PaddleOCR未安装或导入失败。请使用以下命令安装预编译包:\npip install paddlepaddle -i https://mirror.baidu.com/pypi/simple\npip install paddleocr"
            self.progress_log.emit(f"错误: {error_msg}")
            self.finished.emit(False, "PaddleOCR未安装或导入失败")
            return
            
        try:
            self.progress_log.emit(f"开始使用PaddleOCR转换: {os.path.basename(self.pdf_path)}")
            self.progress_value.emit(5)
            
            # 初始化PaddleOCR
            self.progress_log.emit("正在加载PaddleOCR模型...")
            try:
                table_engine = PPStructure(show_log=False, use_gpu=self.use_gpu, table=True)
            except Exception as e:
                self.progress_log.emit(f"初始化PaddleOCR模型失败: {str(e)}")
                self.progress_log.emit("请确保已正确安装PaddleOCR及其依赖，使用预编译包可避免编译问题")
                self.finished.emit(False, f"初始化PaddleOCR模型失败: {str(e)}")
                return
            
            # 处理文件
            self.progress_log.emit(f"正在OCR识别表格...")
            self.progress_value.emit(20)
            result = table_engine(self.pdf_path)
            
            # 检查结果是否为空
            if not result or len(result) == 0:
                self.progress_log.emit("OCR未能识别任何内容，请检查PDF文件是否包含可识别的表格")
                self.finished.emit(False, "OCR未能识别任何内容")
                return
                
            self.progress_log.emit(f"识别完成，发现 {len(result)} 个区域")
            self.progress_value.emit(70)
            
            # 导出结果到Excel
            self.progress_log.emit(f"正在导出结果到Excel...")
            try:
                save_structure_res(result, self.excel_path, self.pdf_path)
            except Exception as e:
                self.progress_log.emit(f"导出到Excel失败: {str(e)}")
                self.finished.emit(False, f"导出到Excel失败: {str(e)}")
                return
            
            self.progress_value.emit(100)
            self.progress_log.emit(f"成功将表格导出到: {self.excel_path}")
            self.finished.emit(True, f"成功将表格导出到: {self.excel_path}")
            
        except Exception as e:
            error_msg = f"使用PaddleOCR转换过程中发生错误: {str(e)}"
            self.progress_log.emit(error_msg)
            self.progress_log.emit("如果是依赖错误，请尝试使用预编译包安装PaddleOCR")
            self.finished.emit(False, error_msg)
    
    def requestInterruption(self):
        self.should_stop = True
        self.progress_log.emit("正在尝试中断转换过程...") 