# PDF表格转Excel工具 - 开发维护文档

本文档为开发者提供代码维护和扩展指南。

## 代码结构

### 主要文件
- `pdf_to_excel_gui.py` - 主程序文件，包含GUI界面和核心功能
- `requirements.txt` - 依赖包列表

### 主要类和功能
- `PdfToExcelApp` - 主应用窗口类
- `ConversionWorker` - Camelot表格提取工作线程
- `PaddleOCRWorker` - OCR表格识别工作线程

## 依赖管理

### 核心依赖
- `PySide6` - GUI框架
- `pandas` - 数据处理
- `openpyxl` - Excel文件操作
- `camelot-py` - PDF表格提取
- `paddleocr` (可选) - OCR表格识别

### 依赖更新注意事项
1. 更新依赖版本时，请在`requirements.txt`中明确指定版本号
2. 对于中文注释，请使用英文替代，避免在不同编码环境下出现问题
3. PaddleOCR相关依赖应保持为可选项，不影响基本功能

## 代码维护指南

### 添加新功能
1. 在适当的类中添加新方法
2. 更新UI组件和事件处理
3. 添加错误处理和日志记录
4. 更新文档

### 错误处理最佳实践
- 使用try-except块捕获预期异常
- 在日志中记录详细错误信息
- 向用户提供清晰的错误消息和解决建议
- 对于关键操作，添加适当的验证

### 线程安全
- 使用QThread和QObject.moveToThread确保UI响应性
- 使用信号槽机制在线程间通信
- 避免直接从工作线程访问UI元素

## 常见开发问题

### 1. Windows环境中的编码问题
在Windows系统中，默认使用GBK编码，这可能导致包含中文注释的Python文件出现编码错误。

**解决方案**:
- 所有源代码文件使用UTF-8编码
- 避免在`requirements.txt`等配置文件中使用中文注释
- 如需使用中文，添加文件头部编码声明：`# -*- coding: utf-8 -*-`

### 2. PaddleOCR集成问题
PaddleOCR依赖较多，且在Windows上可能需要编译。

**解决方案**:
- 保持PaddleOCR为可选功能
- 提供详细的安装指南，建议使用预编译包
- 在代码中添加适当的错误处理和用户提示

### 3. 内存管理
处理大型PDF文件时可能遇到内存问题。

**解决方案**:
- 分批处理大型文件
- 实现内存使用监控
- 在处理大文件前检查可用内存
- 考虑添加临时文件清理机制

## 测试指南

### 单元测试
添加单元测试以验证核心功能：
```python
# test_pdf_to_excel.py
import unittest
from pdf_to_excel_gui import ConversionWorker

class TestConversion(unittest.TestCase):
    def test_conversion(self):
        # 测试代码
        pass
```

### 手动测试清单
1. 基本功能测试
   - 选择PDF文件
   - 设置输出路径
   - 转换表格
   - 验证结果

2. 错误处理测试
   - 无效PDF文件
   - 无表格PDF
   - 权限错误
   - 内存不足

3. 界面响应测试
   - 进度条更新
   - 状态消息
   - 错误提示

## 发布流程

1. 更新版本号
2. 更新`requirements.txt`
3. 更新文档
4. 创建发布标签
5. 打包分发

## 联系与支持

如有任何开发相关问题，请联系项目维护者或提交Issue。 