import sys
import os
import pandas as pd
import camelot
import psutil # 用于获取内存和CPU信息
import numpy as np
import tempfile
import time
import traceback
from pathlib import Path

# PaddleOCR导入与错误处理
PADDLE_AVAILABLE = False
PADDLE_ERROR_MSG = ""
# 检测是否在打包环境中运行
PACKAGED_ENV = getattr(sys, 'frozen', False)

try:
    from paddleocr import PPStructure, save_structure_res
    PADDLE_AVAILABLE = True
except ImportError as e:
    PADDLE_ERROR_MSG = str(e)
    # 在打包环境中，我们假定OCR模块已经包含
    if PACKAGED_ENV:
        print("运行在打包环境中，将尝试延迟导入PaddleOCR")
except Exception as e:
    PADDLE_ERROR_MSG = f"加载PaddleOCR时出错: {str(e)}"

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QLineEdit, QFileDialog, QCheckBox, QMessageBox,
    QStatusBar, QProgressBar, QTextEdit, QComboBox, QGridLayout, 
    QGroupBox, QFrame, QSplitter, QRadioButton, QButtonGroup
)
from PySide6.QtCore import Qt, QThread, Signal, QObject, QTimer
from PySide6.QtGui import QIcon, QFont, QColor, QPalette

# --- 样式表定义 ---
STYLE_SHEET = """
QMainWindow {
    background-color: #f5f5f7;
}

QGroupBox {
    font-weight: bold;
    border: 1px solid #cccccc;
    border-radius: 6px;
    margin-top: 12px;
    padding-top: 10px;
    background-color: #ffffff;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px;
    color: #333333;
}

QPushButton {
    background-color: #4a86e8;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #3a76d8;
}

QPushButton:pressed {
    background-color: #2a66c8;
}

QPushButton:disabled {
    background-color: #cccccc;
    color: #666666;
}

QLineEdit {
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 6px;
    background-color: #ffffff;
}

QLineEdit:read-only {
    background-color: #f0f0f0;
}

QProgressBar {
    border: 1px solid #cccccc;
    border-radius: 4px;
    text-align: center;
    background-color: #f0f0f0;
    height: 20px;
}

QProgressBar::chunk {
    background-color: #4a86e8;
    border-radius: 3px;
}

QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
}

QComboBox {
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 6px;
    background-color: #ffffff;
}

QTextEdit {
    border: 1px solid #cccccc;
    border-radius: 4px;
    background-color: #ffffff;
    font-family: Consolas, Monaco, monospace;
}

QLabel {
    color: #333333;
}

QStatusBar {
    background-color: #f0f0f0;
    color: #333333;
}
"""

# --- 辅助函数 ---
def format_size(bytes_val, suffix="B"):
    """将字节数格式化为可读的大小 (KB, MB, GB)"""
    for unit in ["", "K", "M", "G", "T", "P", "E", "Z"]:
        if abs(bytes_val) < 1024.0:
            return f"{bytes_val:3.1f} {unit}{suffix}"
        bytes_val /= 1024.0
    return f"{bytes_val:.1f} Y{suffix}"

# 获取应用程序路径的辅助函数
def get_app_path():
    if getattr(sys, 'frozen', False):
        # 运行在打包环境中
        return sys._MEIPASS
    else:
        # 运行在开发环境中
        return os.path.dirname(os.path.abspath(__file__))

# --- PaddleOCR表格识别工作线程 ---
class PaddleOCRWorker(QObject):
    finished = Signal(bool, str)
    progress_log = Signal(str)
    progress_value = Signal(int)
    
    def __init__(self, pdf_path, excel_path, single_sheet, use_gpu=False):
        super().__init__()
        self.pdf_path = pdf_path
        self.excel_path = excel_path
        self.single_sheet = single_sheet
        self.use_gpu = use_gpu
        self.should_stop = False
    
    def run(self):
        if not PADDLE_AVAILABLE and not PACKAGED_ENV:
            error_msg = f"PaddleOCR未安装或导入失败。请使用以下命令安装预编译包:\npip install paddlepaddle -i https://mirror.baidu.com/pypi/simple\npip install paddleocr"
            self.progress_log.emit(f"错误: {error_msg}")
            self.finished.emit(False, "PaddleOCR未安装或导入失败")
            return
            
        try:
            self.progress_log.emit(f"开始使用PaddleOCR转换: {os.path.basename(self.pdf_path)}")
            self.progress_value.emit(5)
            
            # 在打包环境中，尝试延迟导入PaddleOCR
            if PACKAGED_ENV and not PADDLE_AVAILABLE:
                self.progress_log.emit("检测到打包环境，正在尝试延迟导入PaddleOCR...")
                try:
                    from paddleocr import PPStructure, save_structure_res
                    self.progress_log.emit("成功导入PaddleOCR!")
                except ImportError as e:
                    self.progress_log.emit(f"延迟导入PaddleOCR失败: {str(e)}")
                    self.finished.emit(False, "无法导入PaddleOCR模块，打包时可能未包含此模块")
                    return
            
            # 初始化PaddleOCR
            self.progress_log.emit("正在加载PaddleOCR模型...")
            try:
                table_engine = PPStructure(show_log=False, use_gpu=self.use_gpu, table=True)
            except Exception as e:
                self.progress_log.emit(f"初始化PaddleOCR模型失败: {str(e)}")
                self.progress_log.emit("请确保已正确安装PaddleOCR及其依赖，使用预编译包可避免编译问题")
                self.finished.emit(False, f"初始化PaddleOCR模型失败: {str(e)}")
                return
            
            # 处理文件
            self.progress_log.emit(f"正在OCR识别表格...")
            self.progress_value.emit(20)
            result = table_engine(self.pdf_path)
            
            # 检查结果是否为空
            if not result or len(result) == 0:
                self.progress_log.emit("OCR未能识别任何内容，请检查PDF文件是否包含可识别的表格")
                self.finished.emit(False, "OCR未能识别任何内容")
                return
                
            self.progress_log.emit(f"识别完成，发现 {len(result)} 个区域")
            self.progress_value.emit(70)
            
            # 导出结果到Excel
            self.progress_log.emit(f"正在导出结果到Excel...")
            try:
                save_structure_res(result, self.excel_path, self.pdf_path)
            except Exception as e:
                self.progress_log.emit(f"导出到Excel失败: {str(e)}")
                self.finished.emit(False, f"导出到Excel失败: {str(e)}")
                return
            
            self.progress_value.emit(100)
            self.progress_log.emit(f"成功将表格导出到: {self.excel_path}")
            self.finished.emit(True, f"成功将表格导出到: {self.excel_path}")
            
        except Exception as e:
            error_msg = f"使用PaddleOCR转换过程中发生错误: {str(e)}"
            self.progress_log.emit(error_msg)
            self.progress_log.emit("如果是依赖错误，请尝试使用预编译包安装PaddleOCR")
            self.finished.emit(False, error_msg)
    
    def requestInterruption(self):
        self.should_stop = True
        self.progress_log.emit("正在尝试中断转换过程...")

# --- 后台转换工作线程 ---
class ConversionWorker(QObject):
    finished = Signal(bool, str)
    progress_log = Signal(str)
    progress_value = Signal(int) # (阶段, 百分比) -> 简化为只传百分比

    def __init__(self, pdf_path, excel_path, single_sheet, flavor='lattice'):
        super().__init__()
        self.pdf_path = pdf_path
        self.excel_path = excel_path
        self.single_sheet = single_sheet
        self.flavor = flavor

    def run(self):
        try:
            self.progress_log.emit(f"开始转换: {os.path.basename(self.pdf_path)} 使用 {self.flavor} 策略.")
            self.progress_value.emit(5) # 初始进度

            self.progress_log.emit(f"阶段1/2: 正在读取 PDF (这可能需要一些时间)...")
            # Camelot 读取 PDF 是一个整体操作，很难细分进度
            # 我们将这部分大致分配 0-30% 的进度
            tables = camelot.read_pdf(self.pdf_path, pages='all', flavor=self.flavor, strip_text='\n')
            self.progress_value.emit(30) # PDF 读取完成

            if tables.n == 0:
                self.progress_log.emit("在 PDF 中未找到任何表格。")
                self.finished.emit(False, "在 PDF 中未找到任何表格。")
                return

            self.progress_log.emit(f"共找到 {tables.n} 个表格。")
            self.progress_log.emit(f"阶段2/2: 正在处理和写入表格到 Excel...")
            # 表格处理和写入分配 30-95% 的进度

            with pd.ExcelWriter(self.excel_path, engine='openpyxl') as writer:
                total_tables = tables.n
                base_progress = 30  # PDF读取完成后的基础进度
                table_processing_progress_range = 65 # 分配给表格处理的进度范围 (30% to 95%)

                if self.single_sheet:
                    self.progress_log.emit("正在将所有表格写入单个 Sheet 'All_Tables'...")
                    current_row = 0
                    sheet_name = 'All_Tables'
                    for i, table in enumerate(tables):
                        self.progress_log.emit(f"处理表格 {i+1}/{total_tables}...")
                        df = table.df
                        # 处理合并单元格：向前填充 (fill down)
                        df.fillna(method='ffill', inplace=True)
                        df.to_excel(writer, sheet_name=sheet_name, startrow=current_row, index=False, header=True)
                        current_row += len(df.index) + 2 # 表格间留出2行空行
                        # 更新进度
                        current_progress = base_progress + int(table_processing_progress_range * (i + 1) / total_tables)
                        self.progress_value.emit(current_progress)
                    self.progress_log.emit(f"所有表格已写入 Sheet: {sheet_name}")
                else:
                    self.progress_log.emit("正在将每个表格写入单独的 Sheet...")
                    for i, table in enumerate(tables):
                        sheet_name = f'Table_{i+1}'
                        self.progress_log.emit(f"处理表格 {i+1}/{total_tables}，写入 Sheet: {sheet_name}...")
                        df = table.df
                        # 处理合并单元格：向前填充 (fill down)
                        df.fillna(method='ffill', inplace=True)
                        df.to_excel(writer, sheet_name=sheet_name, index=False, header=True)
                        # 更新进度
                        current_progress = base_progress + int(table_processing_progress_range * (i + 1) / total_tables)
                        self.progress_value.emit(current_progress)
                    self.progress_log.emit("所有表格已写入单独的 Sheet。")

            self.progress_value.emit(100) # 全部完成
            self.progress_log.emit(f"成功将表格导出到: {self.excel_path}")
            self.finished.emit(True, f"成功将表格导出到: {self.excel_path}")

        except ImportError as e:
            msg = f"导入错误: {str(e)}. "
            if "Ghostscript" in str(e):
                msg += "请确保已安装 Ghostscript 并将其添加到系统 PATH。"
            elif "cv2" in str(e) or "opencv" in str(e):
                 msg += "OpenCV (cv2) 未找到。请尝试 `pip install opencv-python` 或 `pip install camelot-py[cv]`."
            self.progress_log.emit(msg)
            self.finished.emit(False, msg)
        except Exception as e:
            error_msg = f"转换过程中发生错误: {str(e)}"
            self.progress_log.emit(error_msg)
            self.finished.emit(False, error_msg)
        finally:
            # 如果中途失败，不重置为0，让用户看到失败时的进度
            # self.progress_value.emit(0)
            pass


# --- 主窗口 ---
class PdfToExcelApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PDF 表格转 Excel 工具 v3.0 (Camelot)")
        self.setGeometry(100, 100, 600, 750)  # 调整窗口大小

        # 设置应用图标
        app_path = get_app_path()
        icon_path = os.path.join(app_path, "icon.ico")
        
        if os.path.exists(icon_path):
            app_icon = QIcon(icon_path)
            self.setWindowIcon(app_icon)
            print(f"窗口图标已设置: {icon_path}")
        else:
            print(f"窗口图标文件未找到: {icon_path}")
        
        # 应用样式表
        self.setStyleSheet(STYLE_SHEET)

        # 设置应用字体
        app_font = QFont("Microsoft YaHei UI", 9)  # 微软雅黑或系统默认
        QApplication.setFont(app_font)

        self.pdf_path_edit = None
        self.excel_path_edit = None
        self.single_sheet_checkbox = None
        self.flavor_combo = None
        self.convert_button = None
        self.status_bar = None
        self.progress_bar = None
        self.log_display = None

        self.process_memory_label = None
        self.available_memory_label = None
        self.process_cpu_label = None
        self.available_cpu_label = None


        self.thread = None
        self.worker = None
        self.system_stats_timer = None
        self.ps_process = None # For psutil.Process caching

        self.init_ui()
        self.init_system_stats_monitor()

    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)

        # --- 文件选择区 ---
        file_group = QGroupBox("文件选择")
        file_group_layout = QVBoxLayout(file_group)

        pdf_layout = QHBoxLayout()
        pdf_label = QLabel("PDF 文件:")
        pdf_label.setMinimumWidth(70)
        self.pdf_path_edit = QLineEdit()
        self.pdf_path_edit.setReadOnly(True)
        pdf_button = QPushButton("选择...")
        pdf_button.setIcon(QIcon.fromTheme("document-open", QIcon()))
        pdf_button.clicked.connect(self.select_pdf_file)
        pdf_layout.addWidget(pdf_label)
        pdf_layout.addWidget(self.pdf_path_edit)
        pdf_layout.addWidget(pdf_button)
        file_group_layout.addLayout(pdf_layout)

        excel_layout = QHBoxLayout()
        excel_label = QLabel("Excel 文件:")
        excel_label.setMinimumWidth(70)
        self.excel_path_edit = QLineEdit()
        self.excel_path_edit.setPlaceholderText("例如：output.xlsx")
        excel_button = QPushButton("选择保存位置...")
        excel_button.setIcon(QIcon.fromTheme("document-save", QIcon()))
        excel_button.clicked.connect(self.select_excel_file)
        excel_layout.addWidget(excel_label)
        excel_layout.addWidget(self.excel_path_edit)
        excel_layout.addWidget(excel_button)
        file_group_layout.addLayout(excel_layout)
        
        main_layout.addWidget(file_group)

        # --- 选项区 ---
        options_group = QGroupBox("转换选项")
        options_layout = QVBoxLayout(options_group)
        
        # 提取方法选择
        extract_method_layout = QHBoxLayout()
        extract_method_label = QLabel("提取方法:")
        self.method_group = QButtonGroup(self)
        
        self.camelot_radio = QRadioButton("传统方法 (Camelot)")
        self.camelot_radio.setChecked(True)
        self.camelot_radio.toggled.connect(self.update_extract_options)
        self.method_group.addButton(self.camelot_radio)
        
        self.ocr_radio = QRadioButton("OCR方法 (PaddleOCR)")
        # 允许选择OCR方法，但如果未安装则显示提示
        if not PADDLE_AVAILABLE:
            self.ocr_radio.setToolTip("未检测到PaddleOCR，选择此选项后将提示安装: pip install paddlepaddle -i https://mirror.baidu.com/pypi/simple && pip install paddleocr")
        self.ocr_radio.toggled.connect(self.update_extract_options)
        self.method_group.addButton(self.ocr_radio)
        
        extract_method_layout.addWidget(extract_method_label)
        extract_method_layout.addWidget(self.camelot_radio)
        extract_method_layout.addWidget(self.ocr_radio)
        extract_method_layout.addStretch(1)
        options_layout.addLayout(extract_method_layout)
        
        # 表格合并选项
        sheet_option_layout = QHBoxLayout()
        self.single_sheet_checkbox = QCheckBox("所有表格合并到单一 Sheet")
        self.single_sheet_checkbox.setChecked(True)
        sheet_option_layout.addWidget(self.single_sheet_checkbox)
        sheet_option_layout.addStretch(1)
        options_layout.addLayout(sheet_option_layout)
        
        # Camelot提取策略选项
        extract_options_layout = QHBoxLayout()
        self.flavor_label = QLabel("提取策略:")
        self.flavor_combo = QComboBox()
        self.flavor_combo.addItems(["lattice (带线条)", "stream (无明显线条)"])
        
        self.use_gpu_checkbox = QCheckBox("使用GPU加速")
        self.use_gpu_checkbox.setChecked(False)
        self.use_gpu_checkbox.setVisible(False)
        
        extract_options_layout.addWidget(self.flavor_label)
        extract_options_layout.addWidget(self.flavor_combo)
        extract_options_layout.addWidget(self.use_gpu_checkbox)
        extract_options_layout.addStretch(1)
        options_layout.addLayout(extract_options_layout)
        
        main_layout.addWidget(options_group)

        # --- 转换按钮 ---
        self.convert_button = QPushButton("开始转换")
        self.convert_button.setFixedHeight(45)
        self.convert_button.setIcon(QIcon.fromTheme("system-run", QIcon()))
        self.convert_button.clicked.connect(self.start_conversion)
        main_layout.addWidget(self.convert_button)

        # --- 进度条 ---
        progress_group = QGroupBox("转换进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("进度: %p%") # 显示百分比
        progress_layout.addWidget(self.progress_bar)
        
        main_layout.addWidget(progress_group)

        # --- Log 显示区域 ---
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setMinimumHeight(150)
        log_layout.addWidget(self.log_display)
        
        main_layout.addWidget(log_group)

        # --- 系统状态显示 (内存和CPU) ---
        stats_group = QGroupBox("系统状态")
        stats_layout = QGridLayout(stats_group)
        stats_layout.setColumnStretch(2, 1)
        
        self.process_memory_label = QLabel("占用内存: N/A")
        self.available_memory_label = QLabel("可用内存: N/A")
        self.process_cpu_label = QLabel("占用CPU: N/A")
        self.available_cpu_label = QLabel("可用CPU: N/A")

        stats_layout.addWidget(self.process_memory_label, 0, 0)
        stats_layout.addWidget(self.available_memory_label, 0, 1)
        stats_layout.addWidget(self.process_cpu_label, 1, 0)
        stats_layout.addWidget(self.available_cpu_label, 1, 1)
        
        main_layout.addWidget(stats_group)

        # --- 状态栏 ---
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.update_status_bar("准备就绪。")

    def init_system_stats_monitor(self):
        self.ps_process = psutil.Process(os.getpid())
        # Call cpu_percent once at the beginning for a baseline (non-blocking)
        self.ps_process.cpu_percent(interval=None)
        # Call system-wide cpu_percent once with a small interval for baseline
        psutil.cpu_percent(interval=0.1)


        self.system_stats_timer = QTimer(self)
        self.system_stats_timer.timeout.connect(self.update_system_stats)
        self.system_stats_timer.start(2000) # 每2秒更新一次
        self.update_system_stats() # 立即更新一次

    def update_system_stats(self):
        # 内存信息
        mem_info = self.ps_process.memory_info()
        self.process_memory_label.setText(f"占用内存: {format_size(mem_info.rss)}")
        
        virtual_mem = psutil.virtual_memory()
        self.available_memory_label.setText(f"可用内存: {format_size(virtual_mem.available)}")

        # CPU 信息
        # Process CPU (non-blocking after first call)
        process_cpu_usage = self.ps_process.cpu_percent(interval=None)
        self.process_cpu_label.setText(f"占用CPU: {process_cpu_usage:.1f}%")

        # System-wide CPU (blocking for the interval)
        # Use a small interval to avoid freezing GUI too much if timer is frequent
        system_cpu_usage = psutil.cpu_percent(interval=0.1) # Blocks for 0.1s
        available_system_cpu = 100.0 - system_cpu_usage
        self.available_cpu_label.setText(f"可用CPU: {available_system_cpu:.1f}%")


    def append_log(self, message):
        self.log_display.append(message)
        self.log_display.ensureCursorVisible()

    def update_status_bar(self, message):
        self.status_bar.showMessage(message)

    def update_progress_bar(self, value):
        self.progress_bar.setValue(value)
        
        # 根据进度设置不同的样式和文本
        if value < 5:
            self.progress_bar.setStyleSheet("""
                QProgressBar::chunk {
                    background-color: #4a86e8;
                }
            """)
            self.progress_bar.setFormat("准备中: %p%")
        elif value >= 5 and value < 30:
            self.progress_bar.setStyleSheet("""
                QProgressBar::chunk {
                    background-color: #4a86e8;
                }
            """)
            self.progress_bar.setFormat("读取PDF: %p%")
        elif value >= 30 and value < 98:
            self.progress_bar.setStyleSheet("""
                QProgressBar::chunk {
                    background-color: #4a86e8;
                }
            """)
            self.progress_bar.setFormat("处理表格: %p%")
        elif value >= 98:
            self.progress_bar.setStyleSheet("""
                QProgressBar::chunk {
                    background-color: #43a047;
                }
            """)
            self.progress_bar.setFormat("完成: %p%")
        if value == 0:
            self.progress_bar.setStyleSheet("")
            self.progress_bar.setFormat("进度: %p%")


    def select_pdf_file(self):
        # ... (与 v2 相同) ...
        file_path, _ = QFileDialog.getOpenFileName(self, "选择 PDF 文件", "", "PDF 文件 (*.pdf)")
        if file_path:
            self.pdf_path_edit.setText(file_path)
            self.update_status_bar(f"已选择 PDF: {os.path.basename(file_path)}")
            self.append_log(f"选定 PDF: {file_path}")
            if not self.excel_path_edit.text():
                base, _ = os.path.splitext(file_path)
                self.excel_path_edit.setText(base + ".xlsx")
                self.append_log(f"自动填充 Excel 保存路径为: {base + '.xlsx'}")


    def select_excel_file(self):
        # ... (与 v2 相同) ...
        default_name = os.path.basename(self.excel_path_edit.text() or "converted_tables.xlsx")
        default_dir = os.path.dirname(self.excel_path_edit.text()) or os.getcwd()
        default_path = os.path.join(default_dir, default_name)

        file_path, _ = QFileDialog.getSaveFileName(self, "保存 Excel 文件", default_path, "Excel 文件 (*.xlsx)")
        if file_path:
            if not file_path.lower().endswith(".xlsx"):
                file_path += ".xlsx"
            self.excel_path_edit.setText(file_path)
            self.update_status_bar(f"Excel 将保存为: {os.path.basename(file_path)}")
            self.append_log(f"选定 Excel 保存路径: {file_path}")

    def start_conversion(self):
        pdf_path = self.pdf_path_edit.text()
        excel_path = self.excel_path_edit.text()

        if not pdf_path:
            QMessageBox.warning(self, "警告", "请先选择一个 PDF 文件。")
            self.append_log("[警告] 未选择 PDF 文件。")
            return
        if not os.path.exists(pdf_path):
            QMessageBox.warning(self, "警告", f"PDF 文件不存在: {pdf_path}")
            self.append_log(f"[警告] PDF 文件不存在: {pdf_path}")
            return
        if not excel_path:
            QMessageBox.warning(self, "警告", "请输入或选择 Excel 保存路径。")
            self.append_log("[警告] 未指定 Excel 保存路径。")
            return

        excel_dir = os.path.dirname(excel_path)
        if excel_dir and not os.path.exists(excel_dir):
            try:
                os.makedirs(excel_dir, exist_ok=True)
                self.append_log(f"创建目录: {excel_dir}")
            except OSError as e:
                QMessageBox.critical(self, "错误", f"无法创建目录 {excel_dir}: {e}")
                self.append_log(f"[错误] 无法创建目录 {excel_dir}: {e}")
                return

        self.log_display.clear()
        self.append_log("开始准备转换...")
        self.update_progress_bar(0) # 重置进度条格式

        single_sheet = self.single_sheet_checkbox.isChecked()
        self.convert_button.setEnabled(False)
        self.update_status_bar("正在转换...")

        self.thread = QThread()
        
        # 根据选择的方法创建相应的工作线程
        if self.ocr_radio.isChecked():
            # 使用OCR方法 (PaddleOCR)
            # 如果在打包环境中，我们假定OCR模块已包含，允许继续
            if not PADDLE_AVAILABLE and not PACKAGED_ENV:
                error_msg = f"您已选择使用OCR方法，但未检测到PaddleOCR模块。\n\n请使用以下命令安装预编译包:\npip install paddlepaddle -i https://mirror.baidu.com/pypi/simple\npip install paddleocr\n\n安装后重启应用程序即可使用OCR功能。"
                install_msg_box = QMessageBox(self)
                install_msg_box.setIcon(QMessageBox.Information)
                install_msg_box.setWindowTitle("需要安装PaddleOCR")
                install_msg_box.setText("OCR功能需要安装额外模块")
                install_msg_box.setInformativeText(error_msg)
                install_msg_box.setStandardButtons(QMessageBox.Ok)
                copy_button = install_msg_box.addButton("复制安装命令", QMessageBox.ActionRole)
                install_msg_box.exec()
                
                # 如果用户点击了复制按钮，将命令复制到剪贴板
                if install_msg_box.clickedButton() == copy_button:
                    clipboard = QApplication.clipboard()
                    clipboard.setText("pip install paddlepaddle -i https://mirror.baidu.com/pypi/simple && pip install paddleocr")
                    self.append_log("安装命令已复制到剪贴板")
                
                self.append_log(f"[提示] 需要安装PaddleOCR模块才能使用OCR功能")
                self.convert_button.setEnabled(True)
                return
                
            use_gpu = self.use_gpu_checkbox.isChecked()
            self.worker = PaddleOCRWorker(pdf_path, excel_path, single_sheet, use_gpu)
            self.append_log(f"使用PaddleOCR方式转换 (GPU加速: {'是' if use_gpu else '否'})")
        else:
            # 使用传统方法 (Camelot)
            flavor_text = self.flavor_combo.currentText()
            flavor = 'lattice' if 'lattice' in flavor_text else 'stream'
            self.worker = ConversionWorker(pdf_path, excel_path, single_sheet, flavor)
            self.append_log(f"使用Camelot方式转换 (策略: {flavor})")
        
        self.worker.moveToThread(self.thread)
        self.worker.progress_log.connect(self.append_log)
        self.worker.progress_value.connect(self.update_progress_bar)
        self.worker.finished.connect(self.conversion_finished)

        self.thread.started.connect(self.worker.run)
        self.thread.finished.connect(self.thread.deleteLater)
        self.worker.finished.connect(self.thread.quit)
        self.worker.finished.connect(self.worker.deleteLater)

        self.thread.start()

    def conversion_finished(self, success, message):
        self.convert_button.setEnabled(True)
        self.update_status_bar(message)
        if success:
            success_msg = QMessageBox(self)
            success_msg.setWindowTitle("完成")
            success_msg.setText("转换成功")
            success_msg.setInformativeText(message)
            success_msg.setIcon(QMessageBox.Information)
            success_msg.exec()
            self.progress_bar.setValue(100) # 确保成功时进度条满
            self.progress_bar.setFormat("完成: 100%")
        else:
            error_msg = QMessageBox(self)
            error_msg.setWindowTitle("错误")
            error_msg.setText("转换失败")
            error_msg.setInformativeText(message)
            error_msg.setIcon(QMessageBox.Critical)
            error_msg.exec()
            # 失败时进度条保持在失败时的值
        self.append_log(f"转换{'成功' if success else '失败'}: {message}")


    def closeEvent(self, event):
        # ... (与 v2 相同) ...
        if self.system_stats_timer: # 停止定时器
            self.system_stats_timer.stop()

        if self.thread and self.thread.isRunning():
            reply = QMessageBox.question(self, '退出应用',
                                           "转换仍在进行中，确定要退出吗？",
                                           QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.append_log("用户请求退出，正在尝试停止线程...")
                # 尝试更安全地停止 worker
                if hasattr(self.worker, 'requestInterruption'): # 如果worker支持
                    self.worker.requestInterruption()
                self.thread.quit()
                if not self.thread.wait(3000):
                    self.append_log("线程未能及时停止，可能需要强制退出。")
                    self.thread.terminate() # 最后手段
                    self.thread.wait() # 等待终止
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

    def update_extract_options(self):
        """根据选择的提取方法更新UI显示的选项"""
        # 当使用传统方法时
        if self.camelot_radio.isChecked():
            self.flavor_combo.setVisible(True)
            self.flavor_label.setVisible(True)
            self.use_gpu_checkbox.setVisible(False)
        # 当使用OCR方法时
        else:
            self.flavor_combo.setVisible(False)
            self.flavor_label.setVisible(False)
            self.use_gpu_checkbox.setVisible(True)
            self.use_gpu_checkbox.setEnabled(True)  # 确保GPU复选框总是可用


if __name__ == '__main__':
    try:
        app = QApplication(sys.argv)
        window = PdfToExcelApp()
        window.show()
        sys.exit(app.exec())
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        try:
            error_msg_box = QMessageBox()
            error_msg_box.setIcon(QMessageBox.Critical)
            error_msg_box.setText("应用程序启动失败")
            error_msg_box.setInformativeText(f"错误: {e}\n请检查依赖项。")
            error_msg_box.setWindowTitle("启动错误")
            error_msg_box.exec()
        except:
            pass