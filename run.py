#!/usr/bin/env python3
"""
PDF转Excel工具 - 简单启动脚本
=============================

这是一个简单的启动脚本，用于快速测试和运行PDF转Excel工具。

使用方法：
    python run.py
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    """主函数"""
    print("🚀 PDF转Excel工具 v4.0.0 - 简单启动脚本")
    print("=" * 50)
    
    try:
        # 导入主应用程序
        from src import PDFToExcelApp
        
        # 创建应用实例
        print("📦 创建应用实例...")
        app = PDFToExcelApp()
        
        # 初始化应用
        print("🔧 初始化应用...")
        app.initialize()
        
        print("✅ 应用初始化成功！")
        print()
        
        # 显示应用信息
        formats = app.get_supported_formats()
        print("📊 支持的输出格式:")
        for fmt in formats:
            print(f"   - {fmt.upper()}")
        
        print()
        print("🎯 应用程序状态:")
        print(f"   - 初始化状态: {'✅ 已初始化' if app.is_initialized else '❌ 未初始化'}")
        
        # 获取配置信息
        config = app.config
        print(f"   - OCR设备: {config.ocr_engine.device}")
        print(f"   - 支持语言: {config.ocr_engine.language}")
        
        print()
        print("💡 使用建议:")
        print("   1. 使用完整功能: python main.py")
        print("   2. 处理PDF文件: python main.py document.pdf")
        print("   3. 查看帮助: python main.py --help")
        print("   4. 运行测试: python test_end_to_end.py")
        
        print()
        print("🎉 应用程序运行正常！")
        
        return 0
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print()
        print("🔧 故障排除:")
        print("   1. 检查Python版本: python --version (需要3.8+)")
        print("   2. 安装依赖: pip install -r requirements.txt")
        print("   3. 检查PaddleOCR: pip install paddleocr")
        print("   4. 查看详细错误: python run.py --verbose")
        
        if "--verbose" in sys.argv:
            import traceback
            traceback.print_exc()
        
        return 1
    
    finally:
        # 清理资源
        if 'app' in locals():
            try:
                app.cleanup()
                print("🧹 资源清理完成")
            except:
                pass


if __name__ == "__main__":
    sys.exit(main())
