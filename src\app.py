"""
PDF to Excel Application
=========================

Main application class that orchestrates all services and provides the core business logic.
"""

import uuid
from typing import Optional, Dict, Any, List
from pathlib import Path

try:
    # 尝试相对导入（作为模块使用时）
    from .core import Container, ConfigManager, LoggerFactory, get_logger
    from .services import DocumentService, OCRService, ConversionService, ProgressService
    from .domain.models import Document
except ImportError:
    # 绝对导入（直接运行时）
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent))

    from core import Container, ConfigManager, LoggerFactory, get_logger
    from services import DocumentService, OCRService, ConversionService, ProgressService
    from domain.models import Document

logger = get_logger(__name__)


class PDFToExcelApp:
    """Main application class for PDF to Excel conversion."""

    def __init__(self, config_path: Optional[Path] = None):
        self.container = Container()
        self.config_manager = ConfigManager(config_path)
        self.config = None

        # Services
        self.document_service: Optional[DocumentService] = None
        self.ocr_service: Optional[OCRService] = None
        self.conversion_service: Optional[ConversionService] = None
        self.progress_service: Optional[ProgressService] = None

        self._initialized = False

    def initialize(self) -> None:
        """Initialize the application and all services."""
        try:
            logger.info("Initializing PDF to Excel application...")

            # Load configuration
            self.config = self.config_manager.load_config()

            # Configure logging
            LoggerFactory.configure(
                level=self.config.log_level,
                log_file=self.config.temp_dir + "/app.log" if self.config.temp_dir else None,
                enable_console=True,
                enable_file=True,
                structured_logging=False
            )

            # Register services in DI container
            self._register_services()

            # Initialize services
            self._initialize_services()

            self._initialized = True
            logger.info("Application initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize application: {e}")
            raise

    def _register_services(self) -> None:
        """Register all services in the dependency injection container."""
        # Register configuration
        self.container.register_singleton(type(self.config), instance=self.config)

        # Register services
        self.container.register_singleton(
            DocumentService,
            factory=lambda: DocumentService(self.config.__dict__)
        )

        self.container.register_singleton(
            OCRService,
            factory=lambda: OCRService(self.config.__dict__)
        )

        self.container.register_singleton(
            ConversionService,
            factory=lambda: ConversionService(self.config.__dict__)
        )

        self.container.register_singleton(
            ProgressService,
            factory=lambda: ProgressService(self.config.__dict__)
        )

    def _initialize_services(self) -> None:
        """Initialize all services."""
        # Resolve services from container
        self.document_service = self.container.resolve(DocumentService)
        self.ocr_service = self.container.resolve(OCRService)
        self.conversion_service = self.container.resolve(ConversionService)
        self.progress_service = self.container.resolve(ProgressService)

        # Initialize OCR engines
        self.ocr_service.initialize_engines()

    def process_pdf(
        self,
        pdf_path: Path,
        output_format: str = 'xlsx',
        output_path: Optional[Path] = None,
        pages: Optional[List[int]] = None,
        dpi: int = 200
    ) -> Dict[str, Any]:
        """
        Process a PDF file and convert it to the specified format.

        Args:
            pdf_path: Path to the PDF file
            output_format: Output format ('xlsx', 'csv', 'json', 'html')
            output_path: Optional output file path
            pages: Optional list of page numbers to process
            dpi: DPI for image extraction

        Returns:
            Dictionary with processing results
        """
        if not self._initialized:
            raise RuntimeError("Application not initialized. Call initialize() first.")

        # Generate session ID
        session_id = str(uuid.uuid4())

        try:
            logger.info(f"Starting PDF processing: {pdf_path}")

            # Step 1: Load document
            document = self.document_service.load_document(pdf_path)

            # Create progress session
            self.progress_service.create_session(
                session_id=session_id,
                file_path=str(pdf_path),
                total_pages=document.metadata.get('page_count', 0)
            )

            # Step 2: Extract pages as images
            pages_images = list(self.document_service.extract_pages_as_images(
                document=document,
                dpi=dpi,
                pages=pages
            ))

            # Step 3: Process with OCR
            processed_document = self.ocr_service.process_document(
                document=document,
                pages_images=pages_images,
                use_fallback=True
            )

            # Step 4: Convert to output format
            result_path = self.conversion_service.convert_document(
                document=processed_document,
                output_format=output_format,
                output_path=output_path
            )

            # Get processing summary
            session_summary = self.progress_service.get_session_summary(session_id)

            # Cleanup temporary files
            self.document_service.cleanup_temp_files()

            result = {
                'success': True,
                'output_path': str(result_path),
                'session_id': session_id,
                'document_info': {
                    'file_name': processed_document.file_name,
                    'total_pages': processed_document.total_pages,
                    'total_regions': processed_document.total_regions
                },
                'processing_summary': session_summary,
                'statistics': self._generate_statistics(processed_document)
            }

            logger.info(f"PDF processing completed successfully: {result_path}")
            return result

        except Exception as e:
            logger.error(f"PDF processing failed: {e}")

            # Mark session as failed
            self.progress_service.mark_session_failed(session_id, str(e))

            return {
                'success': False,
                'error': str(e),
                'session_id': session_id
            }

    def _generate_statistics(self, document: Document) -> Dict[str, Any]:
        """Generate processing statistics."""
        stats = {
            'total_pages': document.total_pages,
            'total_regions': document.total_regions,
            'regions_by_type': {},
            'pages_with_tables': 0,
            'pages_with_seals': 0,
            'average_regions_per_page': 0
        }

        # Count regions by type
        for page in document.pages:
            for region in page.regions:
                region_type = region.region_type
                stats['regions_by_type'][region_type] = stats['regions_by_type'].get(region_type, 0) + 1

            # Count special page types
            if page.get_regions_by_type('table'):
                stats['pages_with_tables'] += 1
            if page.get_regions_by_type('seal'):
                stats['pages_with_seals'] += 1

        # Calculate averages
        if document.total_pages > 0:
            stats['average_regions_per_page'] = document.total_regions / document.total_pages

        return stats

    def get_processing_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get the current processing status for a session."""
        return self.progress_service.get_session_summary(session_id)

    def cancel_processing(self, session_id: str) -> bool:
        """Cancel an ongoing processing session."""
        try:
            self.progress_service.cancel_session(session_id)
            return True
        except Exception as e:
            logger.error(f"Failed to cancel session {session_id}: {e}")
            return False

    def get_supported_formats(self) -> List[str]:
        """Get list of supported output formats."""
        if not self.conversion_service:
            return ['xlsx', 'csv', 'json', 'html']
        return self.conversion_service.get_supported_formats()

    def get_engine_info(self) -> Dict[str, Any]:
        """Get information about available OCR engines."""
        if not self.ocr_service:
            return {}
        return self.ocr_service.get_engine_info()

    def cleanup(self) -> None:
        """Clean up application resources."""
        try:
            if self.ocr_service:
                self.ocr_service.cleanup()
            if self.document_service:
                self.document_service.cleanup_temp_files()
            if self.progress_service:
                self.progress_service.cleanup_old_sessions()

            logger.info("Application cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    @property
    def is_initialized(self) -> bool:
        """Check if the application is initialized."""
        return self._initialized


def main():
    """简单的主函数，用于直接运行此文件"""
    print("🚀 PDF转Excel工具 v4.0.0")
    print("=" * 40)
    print("提示：建议使用项目根目录的 main.py 获得完整功能")
    print("=" * 40)

    try:
        # 创建应用实例
        app = PDFToExcelApp()
        print("✅ 应用实例创建成功")

        # 初始化应用
        app.initialize()
        print("✅ 应用初始化成功")

        # 显示支持的格式
        formats = app.get_supported_formats()
        print(f"✅ 支持的输出格式: {', '.join(formats)}")

        print("\n🎉 应用程序运行正常！")
        print("💡 使用方法：")
        print("   python main.py                    # 完整的命令行工具")
        print("   python main.py document.pdf      # 处理PDF文件")
        print("   python main.py --help            # 查看帮助")

    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

    finally:
        if 'app' in locals():
            app.cleanup()

    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())