#!/usr/bin/env python3
"""
PDF转Excel工具 - 主程序入口
============================

这是PDF转Excel工具的主程序入口文件，可以直接运行。

使用方法：
    python main.py [选项]

示例：
    python main.py                          # 启动交互式处理
    python main.py input.pdf               # 处理单个PDF文件
    python main.py input.pdf -f xlsx       # 指定输出格式
    python main.py input.pdf -o output/    # 指定输出目录
"""

import sys
import argparse
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

# 现在可以导入我们的模块
from src import PDFToExcelApp


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="PDF转Excel工具 - 智能文档处理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py                           # 交互式模式
  python main.py document.pdf             # 处理单个PDF文件
  python main.py document.pdf -f xlsx     # 指定输出为Excel格式
  python main.py document.pdf -o output/  # 指定输出目录
  python main.py document.pdf -f json -o results/  # JSON格式输出到results目录

支持的输出格式:
  xlsx    Excel格式 (默认)
  xls     Excel 97-2003格式
  csv     CSV格式
  json    JSON格式
  html    HTML格式
        """
    )
    
    parser.add_argument(
        'input_file',
        nargs='?',
        help='要处理的PDF文件路径'
    )
    
    parser.add_argument(
        '-f', '--format',
        choices=['xlsx', 'xls', 'csv', 'json', 'html'],
        default='xlsx',
        help='输出格式 (默认: xlsx)'
    )
    
    parser.add_argument(
        '-o', '--output',
        help='输出目录 (默认: output/)'
    )
    
    parser.add_argument(
        '-c', '--config',
        help='配置文件路径 (默认: config/app_config.yaml)'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='显示详细日志信息'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='PDF转Excel工具 v4.0.0'
    )
    
    return parser.parse_args()


def interactive_mode():
    """交互式模式"""
    print("🚀 PDF转Excel工具 v4.0.0")
    print("=" * 50)
    print()
    
    # 获取输入文件
    while True:
        input_file = input("请输入PDF文件路径 (或输入 'q' 退出): ").strip()
        if input_file.lower() == 'q':
            print("👋 再见！")
            return
        
        input_path = Path(input_file)
        if input_path.exists() and input_path.suffix.lower() == '.pdf':
            break
        else:
            print("❌ 文件不存在或不是PDF文件，请重新输入。")
    
    # 获取输出格式
    print("\n📊 支持的输出格式:")
    formats = ['xlsx', 'xls', 'csv', 'json', 'html']
    for i, fmt in enumerate(formats, 1):
        print(f"  {i}. {fmt.upper()}")
    
    while True:
        choice = input(f"\n请选择输出格式 (1-{len(formats)}, 默认: 1): ").strip()
        if not choice:
            output_format = 'xlsx'
            break
        try:
            idx = int(choice) - 1
            if 0 <= idx < len(formats):
                output_format = formats[idx]
                break
            else:
                print("❌ 无效选择，请输入1-5之间的数字。")
        except ValueError:
            print("❌ 请输入有效的数字。")
    
    # 获取输出目录
    output_dir = input("\n📁 输出目录 (默认: output/): ").strip()
    if not output_dir:
        output_dir = "output"
    
    return input_path, output_format, output_dir


def process_file(app, input_file, output_format, output_dir):
    """处理单个文件"""
    try:
        print(f"🔄 开始处理: {input_file}")
        print(f"📊 输出格式: {output_format.upper()}")
        print(f"📁 输出目录: {output_dir}")
        print()
        
        # 处理文件
        result = app.process_file(
            input_path=input_file,
            output_format=output_format,
            output_dir=output_dir
        )
        
        print("✅ 处理完成！")
        print(f"📄 输出文件: {result.output_path}")
        print(f"⏱️  处理时间: {result.processing_time:.2f}秒")
        
        if hasattr(result, 'statistics'):
            stats = result.statistics
            print(f"📊 处理统计:")
            print(f"   - 总页数: {stats.get('total_pages', 'N/A')}")
            print(f"   - 识别区域: {stats.get('total_regions', 'N/A')}")
            print(f"   - 文本区域: {stats.get('text_regions', 'N/A')}")
            print(f"   - 表格区域: {stats.get('table_regions', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        if args.verbose:
            traceback.print_exc()
        return False


def main():
    """主函数"""
    args = parse_arguments()
    
    try:
        # 创建应用实例
        config_path = Path(args.config) if args.config else None
        app = PDFToExcelApp(config_path)
        
        # 设置日志级别
        if args.verbose:
            import logging
            logging.getLogger('src').setLevel(logging.DEBUG)
        
        print("🚀 初始化PDF转Excel工具...")
        app.initialize()
        print("✅ 初始化完成")
        print()
        
        # 检查是否有输入文件
        if args.input_file:
            # 命令行模式
            input_path = Path(args.input_file)
            if not input_path.exists():
                print(f"❌ 文件不存在: {input_path}")
                return 1
            
            if input_path.suffix.lower() != '.pdf':
                print(f"❌ 不是PDF文件: {input_path}")
                return 1
            
            output_dir = args.output or "output"
            success = process_file(app, input_path, args.format, output_dir)
            return 0 if success else 1
        
        else:
            # 交互式模式
            result = interactive_mode()
            if result:
                input_path, output_format, output_dir = result
                success = process_file(app, input_path, output_format, output_dir)
                return 0 if success else 1
            return 0
    
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        return 0
    
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        if args.verbose if 'args' in locals() else False:
            import traceback
            traceback.print_exc()
        return 1
    
    finally:
        # 清理资源
        if 'app' in locals():
            try:
                app.cleanup()
            except:
                pass


if __name__ == "__main__":
    sys.exit(main())
