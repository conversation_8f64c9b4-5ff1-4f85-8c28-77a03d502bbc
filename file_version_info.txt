# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers和prodvers应该总是一个包含四个项的元组：(1, 2, 3, 4)
    # 分别设置为文件和产品的版本号
    filevers=(3, 0, 0, 0),
    prodvers=(3, 0, 0, 0),
    # 包含一个位掩码，指定此文件的有效性位
    mask=0x3f,
    # 包含一个位掩码，设置该文件的二进制数据属性
    flags=0x0,
    # 操作系统：Windows系统
    OS=0x40004,
    # 文件类型：应用程序
    fileType=0x1,
    # 文件子类型：0
    subtype=0x0,
    # 创建日期
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404b0',
        [StringStruct(u'CompanyName', u'PDF表格转Excel工具'),
        StringStruct(u'FileDescription', u'PDF表格转Excel工具'),
        StringStruct(u'FileVersion', u'3.0.0'),
        StringStruct(u'InternalName', u'PDFtoEXCEL'),
        StringStruct(u'LegalCopyright', u'© 2023 PDF表格转Excel工具. 保留所有权利。'),
        StringStruct(u'OriginalFilename', u'PDFtoEXCEL.exe'),
        StringStruct(u'ProductName', u'PDF表格转Excel工具'),
        StringStruct(u'ProductVersion', u'3.0.0')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [0x0804, 1200])])
  ]
) 