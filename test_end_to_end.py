#!/usr/bin/env python3
"""
End-to-End Test Script
======================

Test the complete PDF to Excel conversion workflow with a sample PDF.
"""

import sys
import os
from pathlib import Path
import traceback
import tempfile

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def create_sample_pdf():
    """Create a simple sample PDF for testing."""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        # Create a temporary PDF file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = Path(temp_file.name)
        temp_file.close()
        
        # Create PDF content
        c = canvas.Canvas(str(temp_path), pagesize=letter)
        width, height = letter
        
        # Add title
        c.setFont("Helvetica-Bold", 16)
        c.drawString(100, height - 100, "PDF to Excel Test Document")
        
        # Add some text
        c.setFont("Helvetica", 12)
        c.drawString(100, height - 150, "This is a test document for the PDF to Excel converter.")
        c.drawString(100, height - 170, "It contains some text and a simple table.")
        
        # Add a simple table
        c.setFont("Helvetica-Bold", 10)
        table_y = height - 220
        c.drawString(100, table_y, "Name")
        c.drawString(200, table_y, "Age")
        c.drawString(300, table_y, "City")
        
        c.setFont("Helvetica", 10)
        c.drawString(100, table_y - 20, "Alice")
        c.drawString(200, table_y - 20, "25")
        c.drawString(300, table_y - 20, "New York")
        
        c.drawString(100, table_y - 40, "Bob")
        c.drawString(200, table_y - 40, "30")
        c.drawString(300, table_y - 40, "London")
        
        c.drawString(100, table_y - 60, "Charlie")
        c.drawString(200, table_y - 60, "35")
        c.drawString(300, table_y - 60, "Tokyo")
        
        c.save()
        
        print(f"✅ Sample PDF created: {temp_path}")
        return temp_path
        
    except ImportError:
        print("⚠️  reportlab not available, creating a text file instead")
        # Create a simple text file as fallback
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.txt', mode='w')
        temp_file.write("PDF to Excel Test Document\n")
        temp_file.write("This is a test document for the PDF to Excel converter.\n")
        temp_file.write("Name,Age,City\n")
        temp_file.write("Alice,25,New York\n")
        temp_file.write("Bob,30,London\n")
        temp_file.write("Charlie,35,Tokyo\n")
        temp_file.close()
        
        temp_path = Path(temp_file.name)
        print(f"✅ Sample text file created: {temp_path}")
        return temp_path

def test_document_service():
    """Test document service functionality."""
    print("\n🔍 Testing Document Service...")
    
    try:
        from src.services import DocumentService
        from src.core import ConfigManager
        
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # Create document service
        doc_service = DocumentService(config.__dict__)
        
        # Create a sample PDF
        sample_pdf = create_sample_pdf()
        
        if sample_pdf.suffix == '.pdf':
            # Test PDF loading
            document = doc_service.load_document(sample_pdf)
            print(f"✅ Document loaded: {document.file_name}")
            print(f"   - Total pages: {document.total_pages}")
            
            # Test page extraction
            pages_images = list(doc_service.extract_pages_as_images(document, dpi=150))
            print(f"✅ Pages extracted: {len(pages_images)} pages")
            
            for page_num, image in pages_images:
                print(f"   - Page {page_num}: {image.shape}")
        else:
            print("⚠️  Skipping PDF tests (using text file)")
        
        # Cleanup
        doc_service.cleanup_temp_files()
        sample_pdf.unlink()  # Delete sample file
        
        return True
        
    except Exception as e:
        print(f"❌ Document service test failed: {e}")
        traceback.print_exc()
        return False

def test_basic_ocr():
    """Test basic OCR functionality."""
    print("\n🔍 Testing Basic OCR...")
    
    try:
        from paddleocr import PaddleOCR
        import numpy as np
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a simple test image with text
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        try:
            # Try to use a system font
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            # Fallback to default font
            font = ImageFont.load_default()
        
        draw.text((50, 50), "Hello World", fill='black', font=font)
        draw.text((50, 100), "PDF to Excel Test", fill='black', font=font)
        
        # Convert to numpy array
        img_array = np.array(img)
        
        # Initialize PaddleOCR
        ocr = PaddleOCR(use_textline_orientation=True, lang='en')

        # Perform OCR
        result = ocr.predict(img_array)
        
        print("✅ OCR completed successfully")
        if result:
            # Handle different result formats
            if isinstance(result, list) and len(result) > 0:
                if isinstance(result[0], list):
                    # Old format: [[bbox, (text, confidence)], ...]
                    for line in result[0]:
                        if len(line) >= 2 and isinstance(line[1], (list, tuple)) and len(line[1]) >= 2:
                            text = line[1][0]
                            confidence = line[1][1]
                            print(f"   - Text: '{text}' (confidence: {confidence:.2f})")
                else:
                    # New format: might be different structure
                    print(f"   - OCR result: {result}")
            else:
                print(f"   - OCR result: {result}")
        else:
            print("   - No text detected")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic OCR test failed: {e}")
        traceback.print_exc()
        return False

def test_conversion_service():
    """Test conversion service functionality."""
    print("\n🔍 Testing Conversion Service...")
    
    try:
        from src.services import ConversionService
        from src.domain.models import Document, Page, Region, BoundingBox
        from src.core import ConfigManager
        from pathlib import Path
        
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # Create conversion service
        conv_service = ConversionService(config.__dict__)
        
        # Create a mock document with some data
        document = Document(file_path=Path("test.pdf"))
        page = Page(page_number=1, width=800, height=1200)
        
        # Add a text region
        text_region = Region(
            region_id="text_1",
            region_type="text",
            bounding_box=BoundingBox(100, 100, 300, 150),
            confidence=0.95,
            content="Sample text content"
        )
        page.add_region(text_region)
        document.add_page(page)
        
        # Test different output formats
        formats_to_test = ['xlsx', 'csv', 'json', 'html']
        
        for format_type in formats_to_test:
            try:
                output_path = conv_service.convert_document(document, format_type)
                print(f"✅ {format_type.upper()} conversion successful: {output_path}")
                
                # Verify file exists and has content
                if output_path.exists() and output_path.stat().st_size > 0:
                    print(f"   - File size: {output_path.stat().st_size} bytes")
                else:
                    print(f"   - ⚠️  File is empty or doesn't exist")
                
            except Exception as e:
                print(f"❌ {format_type.upper()} conversion failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Conversion service test failed: {e}")
        traceback.print_exc()
        return False

def test_progress_service():
    """Test progress tracking service."""
    print("\n🔍 Testing Progress Service...")
    
    try:
        from src.services import ProgressService
        from src.core import ConfigManager
        import uuid
        
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # Create progress service
        progress_service = ProgressService(config.__dict__)
        
        # Create a test session
        session_id = str(uuid.uuid4())
        session = progress_service.create_session(
            session_id=session_id,
            file_path="test.pdf",
            total_pages=3
        )
        
        print(f"✅ Session created: {session_id}")
        print(f"   - Total steps: {session.total_steps}")
        
        # Simulate progress updates
        steps = ["document_loading", "page_extraction", "ocr_processing", "result_conversion"]
        
        for i, step in enumerate(steps):
            progress = (i + 1) * 25.0  # 25%, 50%, 75%, 100%
            from src.services.progress_service import ProcessingStatus
            
            if i == len(steps) - 1:
                status = ProcessingStatus.COMPLETED
            else:
                status = ProcessingStatus.IN_PROGRESS
            
            progress_service.update_step_progress(session_id, step, progress, status)
            
            # Get session summary
            summary = progress_service.get_session_summary(session_id)
            print(f"   - Step '{step}': {progress}% ({status.value})")
        
        final_summary = progress_service.get_session_summary(session_id)
        print(f"✅ Final progress: {final_summary['overall_progress']:.1f}%")
        print(f"   - Status: {final_summary['status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Progress service test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all end-to-end tests."""
    print("🚀 Starting End-to-End Tests")
    print("=" * 50)
    
    tests = [
        test_document_service,
        test_basic_ocr,
        test_conversion_service,
        test_progress_service,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 End-to-End Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All end-to-end tests passed! The system is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
