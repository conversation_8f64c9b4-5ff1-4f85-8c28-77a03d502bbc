"""
Document Domain Models
======================

Domain models representing PDF documents and their structure.
"""

from dataclasses import dataclass, field
from typing import List, Optional, Tuple, Any, Dict
from pathlib import Path
from datetime import datetime
import numpy as np


@dataclass
class BoundingBox:
    """Represents a bounding box with coordinates."""
    x1: float
    y1: float
    x2: float
    y2: float

    @property
    def width(self) -> float:
        """Get the width of the bounding box."""
        return self.x2 - self.x1

    @property
    def height(self) -> float:
        """Get the height of the bounding box."""
        return self.y2 - self.y1

    @property
    def center(self) -> Tuple[float, float]:
        """Get the center point of the bounding box."""
        return ((self.x1 + self.x2) / 2, (self.y1 + self.y2) / 2)

    @property
    def area(self) -> float:
        """Get the area of the bounding box."""
        return self.width * self.height

    def contains_point(self, x: float, y: float) -> bool:
        """Check if a point is inside the bounding box."""
        return self.x1 <= x <= self.x2 and self.y1 <= y <= self.y2

    def intersects(self, other: 'BoundingBox') -> bool:
        """Check if this bounding box intersects with another."""
        return not (self.x2 < other.x1 or other.x2 < self.x1 or
                   self.y2 < other.y1 or other.y2 < self.y1)

    def intersection_area(self, other: 'BoundingBox') -> float:
        """Calculate the intersection area with another bounding box."""
        if not self.intersects(other):
            return 0.0

        x1 = max(self.x1, other.x1)
        y1 = max(self.y1, other.y1)
        x2 = min(self.x2, other.x2)
        y2 = min(self.y2, other.y2)

        return (x2 - x1) * (y2 - y1)

    def union_area(self, other: 'BoundingBox') -> float:
        """Calculate the union area with another bounding box."""
        return self.area + other.area - self.intersection_area(other)

    def iou(self, other: 'BoundingBox') -> float:
        """Calculate Intersection over Union (IoU) with another bounding box."""
        union_area = self.union_area(other)
        if union_area == 0:
            return 0.0
        return self.intersection_area(other) / union_area

    @classmethod
    def from_points(cls, points: List[Tuple[float, float]]) -> 'BoundingBox':
        """Create a bounding box from a list of points."""
        if not points:
            raise ValueError("Points list cannot be empty")

        x_coords = [p[0] for p in points]
        y_coords = [p[1] for p in points]

        return cls(
            x1=min(x_coords),
            y1=min(y_coords),
            x2=max(x_coords),
            y2=max(y_coords)
        )

    @classmethod
    def from_numpy_array(cls, array: np.ndarray) -> 'BoundingBox':
        """Create a bounding box from a numpy array of points."""
        if array.shape[1] != 2:
            raise ValueError("Array must have shape (n, 2)")

        points = [(float(array[i, 0]), float(array[i, 1])) for i in range(array.shape[0])]
        return cls.from_points(points)


@dataclass
class Region:
    """Represents a region within a document page."""
    region_id: str
    region_type: str  # 'text', 'table', 'image', 'seal', etc.
    bounding_box: BoundingBox
    confidence: float = 1.0
    content: Optional[Any] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Post-initialization validation."""
        if not 0 <= self.confidence <= 1:
            raise ValueError("Confidence must be between 0 and 1")

        if not self.region_id:
            raise ValueError("Region ID cannot be empty")


@dataclass
class Page:
    """Represents a single page in a document."""
    page_number: int
    width: float
    height: float
    regions: List[Region] = field(default_factory=list)
    image_data: Optional[np.ndarray] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Post-initialization validation."""
        if self.page_number < 1:
            raise ValueError("Page number must be positive")

        if self.width <= 0 or self.height <= 0:
            raise ValueError("Page dimensions must be positive")

    def add_region(self, region: Region) -> None:
        """Add a region to the page."""
        self.regions.append(region)

    def get_regions_by_type(self, region_type: str) -> List[Region]:
        """Get all regions of a specific type."""
        return [region for region in self.regions if region.region_type == region_type]

    def get_region_by_id(self, region_id: str) -> Optional[Region]:
        """Get a region by its ID."""
        for region in self.regions:
            if region.region_id == region_id:
                return region
        return None

    def get_regions_in_area(self, bounding_box: BoundingBox) -> List[Region]:
        """Get all regions that intersect with a given area."""
        return [
            region for region in self.regions
            if region.bounding_box.intersects(bounding_box)
        ]

    @property
    def aspect_ratio(self) -> float:
        """Get the aspect ratio of the page."""
        return self.width / self.height if self.height > 0 else 0

    @property
    def total_regions(self) -> int:
        """Get the total number of regions on the page."""
        return len(self.regions)


@dataclass
class Document:
    """Represents a complete document."""
    file_path: Path
    pages: List[Page] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)

    def __post_init__(self):
        """Post-initialization validation."""
        if not self.file_path:
            raise ValueError("File path cannot be empty")

    def add_page(self, page: Page) -> None:
        """Add a page to the document."""
        self.pages.append(page)

    def get_page(self, page_number: int) -> Optional[Page]:
        """Get a page by its number."""
        for page in self.pages:
            if page.page_number == page_number:
                return page
        return None

    def get_all_regions_by_type(self, region_type: str) -> List[Tuple[int, Region]]:
        """Get all regions of a specific type across all pages."""
        regions = []
        for page in self.pages:
            for region in page.get_regions_by_type(region_type):
                regions.append((page.page_number, region))
        return regions

    @property
    def total_pages(self) -> int:
        """Get the total number of pages in the document."""
        return len(self.pages)

    @property
    def total_regions(self) -> int:
        """Get the total number of regions across all pages."""
        return sum(page.total_regions for page in self.pages)

    @property
    def file_name(self) -> str:
        """Get the file name without path."""
        return self.file_path.name

    @property
    def file_size(self) -> int:
        """Get the file size in bytes."""
        try:
            return self.file_path.stat().st_size
        except (OSError, AttributeError):
            return 0