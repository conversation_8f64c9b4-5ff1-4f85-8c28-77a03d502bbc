                 ooooo     ooo  ooooooooo.  ooooooo  ooooo
                 `888'     `8'  `888   `Y88. `8888    d8'
                  888       8    888   .d88'   Y888..8P
                  888       8    888ooo88P'     `8888'
                  888       8    888           .8PY888.
                  `88.    .8'    888          d8'  `888b
                    `YbodP'     o888o       o888o  o88888o


                    The Ultimate Packer for eXecutables
   Copyright (c) 1996-2025 <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>
                           https://upx.github.io


.___..        .
  |  |_  _.._ ;_/ __
  |  [ )(_][ )| \_)
--------------------

UPX would not be what it is today without the invaluable help of
everybody who was kind enough to spend time testing it, using it
in applications and reporting bugs.

The following people made especially gracious contributions of their
time and energy in helping to track down bugs, add new features, and
generally assist in the UPX maintainership process:

<PERSON> <<EMAIL>>
  for severals ideas for the Linux version
<PERSON><PERSON> <<EMAIL>> and <PERSON> <<EMAIL>>
  for the /proc/self/fd/X and other Linux suggestions
<PERSON> <<EMAIL>>
  for the Win32 GUI
Atli Mar Gudmundsson <<EMAIL>>
  for several comments on the win32/pe stub
Charles W. Sandmann <<EMAIL>>
  for the idea with the stubless decompressor in djgpp2/coff
Ice
  for debugging the PE headersize problem down
Jens Medoch <<EMAIL>>
  for the ps1/exe format
Joergen Ibsen <<EMAIL>> and d'b
  for the relocation & address optimization ideas
John S. Fine <<EMAIL>>
  for the new version of the dos/exe decompressor
Kornel Pal
  for the EFI support
Lukundoo <<EMAIL>>
  for beta testing
Michael Devore
  for initial dos/exe device driver support
Oleg V. Volkov <<EMAIL>>
  for various FreeBSD specific information
The Owl & G-RoM
  for the --compress-icons fix
Ralph Roth <<EMAIL>>
  for reporting several bugs
Salvador Eduardo Tropea
  for beta testing
Stefan Widmann
  for the win32/pe TLS callback support
The WINE project (https://www.winehq.com/)
  for lots of useful information found in their PE loader sources
Natascha
