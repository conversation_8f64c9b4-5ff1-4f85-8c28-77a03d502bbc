# PDF转Excel工具 - 用户文档

## 📖 概述

PDF转Excel工具是一个现代化的文档处理应用程序，使用先进的PaddleOCR PP-StructureV3技术，能够智能识别PDF文档中的文本、表格和印章，并将其转换为多种格式的结构化数据。

### 🌟 主要特性

- **智能OCR识别**：基于PaddleOCR PP-StructureV3，支持中英文混合识别
- **布局检测**：自动识别文档布局，区分文本、表格、图像等区域
- **表格识别**：专业的表格结构识别，保持原始表格格式
- **印章识别**：专门针对中文印章的文本识别功能
- **多格式输出**：支持Excel、CSV、JSON、HTML等多种输出格式
- **批量处理**：支持多页PDF文档的批量处理
- **进度跟踪**：实时显示处理进度和状态
- **现代化界面**：基于PySide6的现代化用户界面

## 🚀 快速开始

### 系统要求

- **操作系统**：Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **Python版本**：Python 3.8 或更高版本
- **内存**：建议4GB以上RAM
- **存储空间**：至少2GB可用空间（用于OCR模型）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <项目地址>
   cd PDFtoEXCEL
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **首次运行**
   ```bash
   python src/app.py
   ```
   
   首次运行时，系统会自动下载OCR模型（约200MB），请耐心等待。

## 📋 使用指南

### 基本操作流程

1. **启动应用程序**
   - 运行 `python src/app.py` 启动图形界面
   - 或使用命令行模式进行批量处理

2. **选择PDF文件**
   - 点击"选择文件"按钮
   - 支持单个PDF文件或多个文件批量处理
   - 支持的文件格式：PDF

3. **配置处理选项**
   - **输出格式**：选择Excel、CSV、JSON或HTML
   - **OCR语言**：选择中文、英文或中英文混合
   - **处理质量**：选择快速、标准或高质量模式
   - **输出目录**：指定结果文件保存位置

4. **开始处理**
   - 点击"开始转换"按钮
   - 实时查看处理进度
   - 处理完成后自动打开输出目录

### 输出格式说明

#### Excel格式 (.xlsx)
- **多工作表结构**：每页PDF对应一个工作表
- **汇总信息**：包含处理统计和元数据
- **表格保持**：保持原始表格的行列结构
- **格式化**：支持字体、颜色等基本格式

#### CSV格式 (.csv)
- **纯文本数据**：适合数据分析和导入其他系统
- **UTF-8编码**：支持中文字符
- **表格合并**：多个表格会合并到单个CSV文件

#### JSON格式 (.json)
- **结构化数据**：完整保留文档结构信息
- **元数据丰富**：包含置信度、坐标等详细信息
- **程序友好**：适合程序化处理和API集成

#### HTML格式 (.html)
- **可视化展示**：在浏览器中查看处理结果
- **样式美化**：包含CSS样式，美观易读
- **交互功能**：支持表格排序、搜索等功能

## ⚙️ 高级配置

### 配置文件

应用程序使用YAML配置文件 `config/app_config.yaml`：

```yaml
# OCR引擎配置
ocr_engine:
  device: "cpu"          # 使用CPU或GPU
  use_gpu: false         # 是否启用GPU加速
  cpu_threads: 8         # CPU线程数

# 布局检测配置
layout_detection:
  model_name: "PP-DocLayout-S"
  threshold: 0.5
  enable_nms: true

# 表格识别配置
table_recognition:
  model_name: "SLANet_plus"
  threshold: 0.5
  merge_tables: true

# 印章识别配置
seal_recognition:
  enable: true
  threshold: 0.7
```

### 环境变量

可以通过环境变量覆盖配置：

```bash
# 设置设备类型
export OCR_DEVICE=gpu

# 设置输出目录
export OUTPUT_DIR=/path/to/output

# 设置日志级别
export LOG_LEVEL=DEBUG
```

## 🔧 故障排除

### 常见问题

#### 1. OCR模型下载失败
**问题**：首次运行时模型下载中断或失败
**解决方案**：
- 检查网络连接
- 使用VPN或更换网络环境
- 手动下载模型文件到 `~/.paddlex/official_models/` 目录

#### 2. 内存不足错误
**问题**：处理大型PDF文件时出现内存错误
**解决方案**：
- 减少并行处理的页面数量
- 降低图像DPI设置（默认200，可降至150）
- 分批处理大型文档

#### 3. 识别准确率低
**问题**：OCR识别结果不准确
**解决方案**：
- 确保PDF文档清晰度足够
- 尝试不同的处理质量设置
- 检查文档是否为扫描版PDF
- 考虑预处理图像（去噪、增强对比度）

#### 4. 表格格式错乱
**问题**：表格识别后格式不正确
**解决方案**：
- 启用表格合并功能
- 调整表格识别阈值
- 使用Camelot引擎作为备选方案

### 日志查看

应用程序会生成详细的日志文件：

- **位置**：`logs/app.log`
- **级别**：INFO, WARNING, ERROR, DEBUG
- **格式**：时间戳 - 模块 - 级别 - 消息

查看实时日志：
```bash
tail -f logs/app.log
```

## 📊 性能优化

### 处理速度优化

1. **使用GPU加速**（如果可用）
   ```yaml
   ocr_engine:
     device: "gpu"
     use_gpu: true
   ```

2. **调整并行处理**
   ```yaml
   processing:
     max_workers: 4      # 根据CPU核心数调整
     batch_size: 2       # 每批处理的页面数
   ```

3. **降低图像质量**（牺牲精度换取速度）
   ```yaml
   document_processing:
     dpi: 150           # 默认200，可降至150或100
     image_quality: 85  # JPEG质量，默认95
   ```

### 内存使用优化

1. **启用内存清理**
   ```yaml
   memory_management:
     auto_cleanup: true
     cleanup_interval: 100  # 每处理100页清理一次
   ```

2. **限制缓存大小**
   ```yaml
   cache:
     max_size: 1000     # 最大缓存项目数
     ttl: 3600         # 缓存生存时间（秒）
   ```

## 🆘 技术支持

### 获取帮助

1. **查看日志**：首先检查 `logs/app.log` 中的错误信息
2. **检查配置**：确认 `config/app_config.yaml` 配置正确
3. **版本信息**：运行 `python --version` 和 `pip list` 检查环境
4. **系统信息**：提供操作系统版本和硬件配置

### 报告问题

报告问题时请提供：
- 详细的错误描述
- 完整的错误日志
- 系统环境信息
- 问题PDF文件（如果可能）
- 复现步骤

## 📈 更新日志

### v4.0.0 (2025-09-10)
- 🎉 完全重构为现代化架构
- ✨ 集成PaddleOCR PP-StructureV3
- 🚀 新增依赖注入和事件驱动架构
- 📊 支持多种输出格式
- 🎯 改进的进度跟踪系统
- 🔧 完善的配置管理
- 📝 结构化日志系统

### v3.x.x (历史版本)
- 基础OCR功能
- 简单的PDF处理
- Excel输出支持

---

**版权信息**：© 2025 PDF转Excel工具。保留所有权利。
**许可证**：MIT License
**技术支持**：请通过GitHub Issues提交问题和建议。
