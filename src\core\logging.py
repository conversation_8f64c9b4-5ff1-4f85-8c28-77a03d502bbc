"""
Logging System
==============

Provides structured logging with support for:
- Multiple output targets (console, file)
- Configurable log levels
- Structured log formatting
- Thread-safe logging
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime
import json


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging."""

    def __init__(self, include_extra: bool = True):
        super().__init__()
        self.include_extra = include_extra

    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON."""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }

        # Add exception information if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)

        # Add extra fields if enabled
        if self.include_extra:
            extra_fields = {
                key: value for key, value in record.__dict__.items()
                if key not in {
                    'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                    'filename', 'module', 'lineno', 'funcName', 'created',
                    'msecs', 'relativeCreated', 'thread', 'threadName',
                    'processName', 'process', 'getMessage', 'exc_info',
                    'exc_text', 'stack_info'
                }
            }
            if extra_fields:
                log_entry['extra'] = extra_fields

        return json.dumps(log_entry, ensure_ascii=False)


class ColoredConsoleFormatter(logging.Formatter):
    """Colored console formatter for better readability."""

    # ANSI color codes
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Green
        'WARNING': '\033[33m',    # Yellow
        'ERROR': '\033[31m',      # Red
        'CRITICAL': '\033[35m',   # Magenta
        'RESET': '\033[0m'        # Reset
    }

    def __init__(self):
        super().__init__(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

    def format(self, record: logging.LogRecord) -> str:
        """Format log record with colors."""
        # Add color to level name
        level_color = self.COLORS.get(record.levelname, '')
        reset_color = self.COLORS['RESET']

        # Create colored record
        colored_record = logging.makeLogRecord(record.__dict__)
        colored_record.levelname = f"{level_color}{record.levelname}{reset_color}"

        return super().format(colored_record)


class LoggerFactory:
    """Factory for creating configured loggers."""

    _configured_loggers: Dict[str, logging.Logger] = {}
    _default_level = logging.INFO
    _log_file: Optional[Path] = None
    _enable_console = True
    _enable_file = False
    _structured_logging = False

    @classmethod
    def configure(
        cls,
        level: str = "INFO",
        log_file: Optional[str] = None,
        enable_console: bool = True,
        enable_file: bool = False,
        structured_logging: bool = False,
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5
    ):
        """Configure global logging settings."""
        cls._default_level = getattr(logging, level.upper())
        cls._log_file = Path(log_file) if log_file else None
        cls._enable_console = enable_console
        cls._enable_file = enable_file
        cls._structured_logging = structured_logging

        # Clear existing loggers to reconfigure them
        cls._configured_loggers.clear()

        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(cls._default_level)

        # Remove existing handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # Add console handler
        if cls._enable_console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(cls._default_level)

            if cls._structured_logging:
                console_handler.setFormatter(StructuredFormatter())
            else:
                console_handler.setFormatter(ColoredConsoleFormatter())

            root_logger.addHandler(console_handler)

        # Add file handler
        if cls._enable_file and cls._log_file:
            # Ensure log directory exists
            cls._log_file.parent.mkdir(parents=True, exist_ok=True)

            file_handler = logging.handlers.RotatingFileHandler(
                cls._log_file,
                maxBytes=max_file_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(cls._default_level)
            file_handler.setFormatter(StructuredFormatter())

            root_logger.addHandler(file_handler)

    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        """Get a configured logger instance."""
        if name not in cls._configured_loggers:
            logger = logging.getLogger(name)
            logger.setLevel(cls._default_level)
            cls._configured_loggers[name] = logger

        return cls._configured_loggers[name]

    @classmethod
    def create_context_logger(cls, name: str, context: Dict[str, Any]) -> 'ContextLogger':
        """Create a context logger with additional fields."""
        base_logger = cls.get_logger(name)
        return ContextLogger(base_logger, context)


class ContextLogger:
    """Logger wrapper that adds context information to all log messages."""

    def __init__(self, logger: logging.Logger, context: Dict[str, Any]):
        self._logger = logger
        self._context = context

    def _log_with_context(self, level: int, msg: str, *args, **kwargs):
        """Log message with context information."""
        # Merge context with any extra fields
        extra = kwargs.get('extra', {})
        extra.update(self._context)
        kwargs['extra'] = extra

        self._logger.log(level, msg, *args, **kwargs)

    def debug(self, msg: str, *args, **kwargs):
        """Log debug message with context."""
        self._log_with_context(logging.DEBUG, msg, *args, **kwargs)

    def info(self, msg: str, *args, **kwargs):
        """Log info message with context."""
        self._log_with_context(logging.INFO, msg, *args, **kwargs)

    def warning(self, msg: str, *args, **kwargs):
        """Log warning message with context."""
        self._log_with_context(logging.WARNING, msg, *args, **kwargs)

    def error(self, msg: str, *args, **kwargs):
        """Log error message with context."""
        self._log_with_context(logging.ERROR, msg, *args, **kwargs)

    def critical(self, msg: str, *args, **kwargs):
        """Log critical message with context."""
        self._log_with_context(logging.CRITICAL, msg, *args, **kwargs)

    def exception(self, msg: str, *args, **kwargs):
        """Log exception message with context."""
        kwargs['exc_info'] = True
        self._log_with_context(logging.ERROR, msg, *args, **kwargs)


# Convenience function for getting loggers
def get_logger(name: str) -> logging.Logger:
    """Get a configured logger instance."""
    return LoggerFactory.get_logger(name)