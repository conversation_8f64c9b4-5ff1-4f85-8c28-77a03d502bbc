"""
OCR Processing Service
======================

Service for orchestrating OCR operations using different engines.
"""

import time
from typing import List, Optional, Dict, Any, Union
from pathlib import Path
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from ..engines import OCREngineBase, PaddleOCREngine, CamelotPDFEngine
from ..domain.models import Document, Page, OCRResult, Region
from ..core.logging import get_logger
from ..core.events import publish_event, ProcessingProgressEvent, OCRResultEvent

logger = get_logger(__name__)


class OCRService:
    """Service for OCR processing operations."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.primary_engine: Optional[OCREngineBase] = None
        self.fallback_engine: Optional[CamelotPDFEngine] = None
        self.max_workers = config.get('max_concurrent_tasks', 4)
        self._lock = threading.RLock()

    def initialize_engines(self) -> None:
        """Initialize OCR engines."""
        try:
            logger.info("Initializing OCR engines...")

            # Initialize primary PaddleOCR engine
            paddle_config = {
                'ocr_engine': self.config.get('ocr_engine', {}),
                'layout_detection': self.config.get('layout_detection', {}),
                'table_recognition': self.config.get('table_recognition', {}),
                'seal_recognition': self.config.get('seal_recognition', {})
            }

            self.primary_engine = PaddleOCREngine(paddle_config)
            self.primary_engine.initialize()

            # Initialize fallback Camelot engine
            camelot_config = self.config.get('camelot', {})
            self.fallback_engine = CamelotPDFEngine(camelot_config)
            self.fallback_engine.initialize()

            logger.info("OCR engines initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize OCR engines: {e}")
            raise

    def process_document(
        self,
        document: Document,
        pages_images: List[tuple[int, np.ndarray]],
        use_fallback: bool = True
    ) -> Document:
        """Process document with OCR and return updated document with results."""
        if not self.primary_engine or not self.primary_engine.is_initialized:
            raise RuntimeError("Primary OCR engine not initialized")

        logger.info(f"Processing document with {len(pages_images)} pages")

        # Process pages with OCR
        if self.max_workers > 1:
            ocr_results = self._process_pages_parallel(pages_images)
        else:
            ocr_results = self._process_pages_sequential(pages_images)

        # Update document with OCR results
        for page_num, image in pages_images:
            page = Page(
                page_number=page_num,
                width=float(image.shape[1]),
                height=float(image.shape[0]),
                image_data=image
            )

            # Find corresponding OCR result
            ocr_result = next((r for r in ocr_results if r.page_number == page_num), None)
            if ocr_result:
                # Convert OCR results to regions
                regions = self._convert_ocr_to_regions(ocr_result)
                page.regions.extend(regions)

            document.add_page(page)

        # Try fallback engine for tables if primary failed
        if use_fallback and self.fallback_engine and self.fallback_engine.is_initialized:
            self._enhance_with_fallback(document)

        logger.info(f"Document processing completed: {document.total_regions} regions found")
        return document

    def _process_pages_sequential(self, pages_images: List[tuple[int, np.ndarray]]) -> List[OCRResult]:
        """Process pages sequentially."""
        results = []

        for i, (page_num, image) in enumerate(pages_images):
            try:
                logger.info(f"Processing page {page_num}")

                # Process with primary engine
                result = self.primary_engine.process_page(image, page_num)
                results.append(result)

                # Publish progress and result events
                progress = (i + 1) / len(pages_images) * 100
                publish_event(ProcessingProgressEvent(
                    current_step=f"OCR processing page {page_num}",
                    progress_percentage=progress,
                    source="OCRService"
                ))

                publish_event(OCRResultEvent(
                    engine_type=self.primary_engine.engine_name,
                    result_data=result,
                    processing_time=result.processing_time,
                    source="OCRService"
                ))

            except Exception as e:
                logger.error(f"Failed to process page {page_num}: {e}")
                # Create empty result for failed page
                results.append(OCRResult(page_number=page_num))

        return results

    def _process_pages_parallel(self, pages_images: List[tuple[int, np.ndarray]]) -> List[OCRResult]:
        """Process pages in parallel."""
        results = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all pages for processing
            future_to_page = {
                executor.submit(self._process_single_page, page_num, image): page_num
                for page_num, image in pages_images
            }

            # Collect results as they complete
            completed = 0
            for future in as_completed(future_to_page):
                page_num = future_to_page[future]

                try:
                    result = future.result()
                    results.append(result)

                    completed += 1
                    progress = completed / len(pages_images) * 100

                    publish_event(ProcessingProgressEvent(
                        current_step=f"OCR processing page {page_num}",
                        progress_percentage=progress,
                        source="OCRService"
                    ))

                    publish_event(OCRResultEvent(
                        engine_type=self.primary_engine.engine_name,
                        result_data=result,
                        processing_time=result.processing_time,
                        source="OCRService"
                    ))

                except Exception as e:
                    logger.error(f"Failed to process page {page_num}: {e}")
                    results.append(OCRResult(page_number=page_num))

        # Sort results by page number
        results.sort(key=lambda x: x.page_number)
        return results

    def _process_single_page(self, page_num: int, image: np.ndarray) -> OCRResult:
        """Process a single page with thread safety."""
        with self._lock:
            logger.debug(f"Processing page {page_num} in thread")
            return self.primary_engine.process_page(image, page_num)

    def _convert_ocr_to_regions(self, ocr_result: OCRResult) -> List[Region]:
        """Convert OCR results to Region objects."""
        regions = []
        region_id_counter = 1

        # Add text regions
        for text_result in ocr_result.text_results:
            region = Region(
                region_id=f"text_{region_id_counter}",
                region_type="text",
                bounding_box=text_result.bounding_box,
                confidence=text_result.confidence,
                content=text_result.text
            )
            regions.append(region)
            region_id_counter += 1

        # Add table regions
        for table_result in ocr_result.table_results:
            region = Region(
                region_id=f"table_{region_id_counter}",
                region_type="table",
                bounding_box=table_result.table_bbox,
                confidence=table_result.confidence,
                content=table_result
            )
            regions.append(region)
            region_id_counter += 1

        # Add seal regions
        for seal_result in ocr_result.seal_results:
            region = Region(
                region_id=f"seal_{region_id_counter}",
                region_type="seal",
                bounding_box=seal_result.seal_bbox,
                confidence=seal_result.confidence,
                content=seal_result
            )
            regions.append(region)
            region_id_counter += 1

        return regions

    def _enhance_with_fallback(self, document: Document) -> None:
        """Enhance document with fallback engine results."""
        try:
            logger.info("Enhancing results with fallback engine")

            # Use Camelot for additional table extraction
            fallback_results = self.fallback_engine.process_pdf(str(document.file_path))

            if fallback_results.get('tables'):
                logger.info(f"Fallback engine found {len(fallback_results['tables'])} additional tables")

                # Add fallback table results to document
                # This is a simplified implementation - in practice, you'd want to
                # merge results more intelligently
                for page in document.pages:
                    for table_result in fallback_results['tables']:
                        region = Region(
                            region_id=f"fallback_table_{len(page.regions) + 1}",
                            region_type="table",
                            bounding_box=table_result.table_bbox,
                            confidence=table_result.confidence,
                            content=table_result,
                            metadata={'source': 'camelot_fallback'}
                        )
                        page.add_region(region)

        except Exception as e:
            logger.warning(f"Fallback engine enhancement failed: {e}")

    def get_engine_info(self) -> Dict[str, Any]:
        """Get information about available engines."""
        info = {
            'primary_engine': None,
            'fallback_engine': None,
            'max_workers': self.max_workers
        }

        if self.primary_engine:
            info['primary_engine'] = {
                'name': self.primary_engine.engine_name,
                'features': self.primary_engine.supported_features,
                'initialized': self.primary_engine.is_initialized
            }

        if self.fallback_engine:
            info['fallback_engine'] = {
                'name': 'Camelot',
                'initialized': self.fallback_engine.is_initialized
            }

        return info

    def cleanup(self) -> None:
        """Clean up OCR engines."""
        try:
            if self.primary_engine:
                self.primary_engine.cleanup()
            if self.fallback_engine:
                self.fallback_engine.cleanup()
            logger.info("OCR engines cleaned up successfully")
        except Exception as e:
            logger.error(f"Error during OCR engines cleanup: {e}")