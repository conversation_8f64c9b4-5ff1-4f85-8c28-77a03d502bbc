"""
Camelot Table Extraction Engine
===============================

Camelot-based table extraction engine as a fallback option.
"""

import time
from typing import List, Optional, Dict, Any
import numpy as np
from pathlib import Path
import tempfile
import os

try:
    import camelot
    import fitz  # PyMuPDF
    CAMELOT_AVAILABLE = True
except ImportError:
    CAMELOT_AVAILABLE = False

from ..base import TableRecognitionEngine
from ...domain.models import TableResult, TableCell, BoundingBox
from ...core.logging import get_logger

logger = get_logger(__name__)


class CamelotTableEngine(TableRecognitionEngine):
    """Camelot-based table extraction engine."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self._initialized = False

    def initialize(self) -> None:
        """Initialize the Camelot engine."""
        if not CAMELOT_AVAILABLE:
            raise RuntimeError("Camelot is not available. Please install camelot-py[cv] and PyMuPDF.")

        self._initialized = True
        logger.info("Camelot table extraction engine initialized successfully")

    def recognize_tables(self, image: np.ndarray, table_regions: List[Dict[str, Any]]) -> List[TableResult]:
        """Extract tables using Camelot from PDF regions."""
        if not self._initialized:
            raise RuntimeError("Engine not initialized")

        # Note: Camelot works directly with PDF files, not images
        # This is a simplified implementation for compatibility
        logger.warning("Camelot engine requires PDF files, not images. Returning empty results.")
        return []

    def extract_tables_from_pdf(self, pdf_path: str, pages: Optional[List[int]] = None) -> List[TableResult]:
        """Extract tables directly from PDF file using Camelot."""
        if not self._initialized:
            raise RuntimeError("Engine not initialized")

        try:
            start_time = time.time()

            # Determine pages to process
            page_list = pages if pages else 'all'

            # Extract tables using Camelot
            tables = camelot.read_pdf(
                pdf_path,
                pages=str(page_list) if isinstance(page_list, list) else page_list,
                flavor=self.config.get('flavor', 'lattice'),  # 'lattice' or 'stream'
                table_areas=self.config.get('table_areas'),
                columns=self.config.get('columns'),
                split_text=self.config.get('split_text', False),
                flag_size=self.config.get('flag_size', False),
                strip_text=self.config.get('strip_text', '\n'),
                row_tol=self.config.get('row_tol', 2),
                column_tol=self.config.get('column_tol', 0)
            )

            processing_time = time.time() - start_time

            # Convert Camelot tables to our format
            table_results = []

            for i, table in enumerate(tables):
                # Get table data
                df = table.df

                # Create table cells
                cells = []
                for row_idx, row in df.iterrows():
                    for col_idx, cell_value in enumerate(row):
                        cell = TableCell(
                            row=row_idx,
                            col=col_idx,
                            text=str(cell_value) if cell_value is not None else "",
                            confidence=table.accuracy / 100.0 if hasattr(table, 'accuracy') else 1.0
                        )
                        cells.append(cell)

                # Get table bounding box from Camelot
                bbox = table._bbox if hasattr(table, '_bbox') else [0, 0, 100, 100]

                table_result = TableResult(
                    cells=cells,
                    table_bbox=BoundingBox(
                        x1=bbox[0], y1=bbox[1],
                        x2=bbox[2], y2=bbox[3]
                    ),
                    confidence=table.accuracy / 100.0 if hasattr(table, 'accuracy') else 1.0,
                    processing_time=processing_time / len(tables),
                    model_name="Camelot"
                )
                table_results.append(table_result)

            logger.info(f"Extracted {len(table_results)} tables from PDF in {processing_time:.2f}s")
            return table_results

        except Exception as e:
            logger.error(f"Camelot table extraction failed: {e}")
            raise

    @property
    def output_formats(self) -> List[str]:
        """Get list of supported output formats."""
        return ['dataframe', 'csv', 'json', 'html']

    def cleanup(self) -> None:
        """Clean up resources."""
        self._initialized = False


class CamelotPDFEngine:
    """Complete PDF processing engine using Camelot."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.table_engine = CamelotTableEngine(config.get('camelot', {}))
        self._initialized = False

    def initialize(self) -> None:
        """Initialize the PDF engine."""
        self.table_engine.initialize()
        self._initialized = True
        logger.info("Camelot PDF engine initialized successfully")

    def process_pdf(self, pdf_path: str, pages: Optional[List[int]] = None) -> Dict[str, Any]:
        """Process PDF file and extract tables."""
        if not self._initialized:
            raise RuntimeError("Engine not initialized")

        try:
            # Extract tables using Camelot
            table_results = self.table_engine.extract_tables_from_pdf(pdf_path, pages)

            # Get PDF metadata
            pdf_info = self._get_pdf_info(pdf_path)

            return {
                'tables': table_results,
                'pdf_info': pdf_info,
                'total_tables': len(table_results)
            }

        except Exception as e:
            logger.error(f"PDF processing failed: {e}")
            raise

    def _get_pdf_info(self, pdf_path: str) -> Dict[str, Any]:
        """Get PDF metadata."""
        try:
            if CAMELOT_AVAILABLE:
                doc = fitz.open(pdf_path)
                info = {
                    'page_count': doc.page_count,
                    'title': doc.metadata.get('title', ''),
                    'author': doc.metadata.get('author', ''),
                    'subject': doc.metadata.get('subject', ''),
                    'creator': doc.metadata.get('creator', ''),
                    'producer': doc.metadata.get('producer', ''),
                    'creation_date': doc.metadata.get('creationDate', ''),
                    'modification_date': doc.metadata.get('modDate', '')
                }
                doc.close()
                return info
        except Exception as e:
            logger.warning(f"Failed to get PDF info: {e}")

        return {'page_count': 0}

    def cleanup(self) -> None:
        """Clean up resources."""
        if hasattr(self, 'table_engine'):
            self.table_engine.cleanup()
        self._initialized = False

    @property
    def is_initialized(self) -> bool:
        """Check if engine is initialized."""
        return self._initialized