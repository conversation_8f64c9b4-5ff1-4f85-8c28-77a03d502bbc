# PDF表格转Excel工具 - 优化打包说明

本文档提供使用`optimize_build.bat`优化打包脚本的详细说明，帮助您创建体积最小的可执行文件。

## 使用方法

1. 双击运行`optimize_build.bat`脚本
2. 按照提示选择打包选项
3. 等待打包完成

## 打包选项说明

### 1. 打包模式选择

- **标准模式**：提供完整打包选项供您选择
- **最小模式**：直接创建体积最小的可执行文件（不含OCR, 文件夹模式, UPX压缩）

### 2. OCR功能选择

- **包含OCR功能**：完整功能，但体积较大（增加约200-300MB）
- **不包含OCR功能**：体积更小，但无法使用OCR表格识别功能

### 3. 打包结构选择

- **单个EXE文件**：便于分发，用户只需一个文件即可运行，但体积较大，启动较慢
- **文件夹模式**：体积更小，启动更快，但分发时需要整个文件夹

### 4. UPX压缩选择

- **使用UPX压缩**：进一步减小体积（约10-30%），但需要安装UPX并可能增加打包时间
- **不使用UPX压缩**：打包更快，但体积略大

### 5. 图标选择

- **使用默认图标**：使用项目目录中的icon.ico
- **使用自定义图标**：可以指定任意绝对路径的图标文件（如 C:\Users\<USER>\Desktop\icon.ico）

## 获得最小体积的推荐设置

要获得体积最小的可执行文件，推荐以下设置：

1. **选择最小模式**直接打包，或选择标准模式并使用以下设置：
   - **不包含OCR功能**
   - **文件夹模式**
   - **使用UPX压缩**
   - **启用激进的模块排除**

这种配置下的体积通常可以减少到原始体积的50%以下。

## UPX压缩说明

如果您选择使用UPX压缩，需要先安装UPX并将其添加到环境变量，或将UPX可执行文件放在名为"UPX"的子目录中。

UPX下载地址：https://github.com/upx/upx/releases

## OCR功能说明

如果您选择包含OCR功能，脚本会自动：
1. 收集所有PaddleOCR和PaddlePaddle相关模块
2. 自动定位并包含PaddleOCR的模型文件
3. 确保OCR功能在打包后可以正常使用

请注意：包含OCR功能会显著增大打包后的体积（200-300MB），但能提供完整的表格OCR识别功能。

## 图标设置说明

图标文件既用于应用程序图标，也会作为数据文件打包进应用程序，确保窗口图标和任务栏图标都能正确显示。您可以：

1. 使用项目目录中的默认图标
2. 提供自定义图标文件的绝对路径（如 C:\Users\<USER>\Desktop\icon.ico）

## 常见问题

### 打包后程序无法运行

1. 确认已安装必要的外部依赖（如Ghostscript）
2. 尝试在命令行中运行exe以查看详细错误信息
3. 检查是否有杀毒软件拦截程序运行

### 打包后OCR功能不可用

1. 确保在打包时选择了"包含OCR功能"选项
2. 确认您的环境中已正确安装paddleocr和paddlepaddle
3. 尝试在命令行运行exe，查看是否有关于OCR的详细错误信息
4. 检查是否有额外的OCR模型文件需要手动添加

### 打包过程中出错

1. 确保安装了最新版本的PyInstaller
2. 检查Python环境是否完整
3. 查看错误日志，解决相应依赖问题

### 如何减小不包含OCR版本的体积

不包含OCR版本已经排除了以下模块：
- paddleocr
- paddlepaddle
- matplotlib
- tkinter
- scipy
- pydoc

如果您发现其他可以安全排除的大型依赖，可以在脚本中的`--exclude-module`部分添加。

## 注意事项

- 打包过程可能需要几分钟至几十分钟，取决于您的计算机性能和选择的打包选项
- 确保在运行脚本前已安装所有必要的Python依赖（可通过`pip install -r requirements.txt`安装）
- 打包后的程序在运行时仍需用户安装如Ghostscript等外部依赖
- 如果图标不显示，请确保提供了有效的.ico文件（可以使用在线转换工具将jpg/png转换为ico） 