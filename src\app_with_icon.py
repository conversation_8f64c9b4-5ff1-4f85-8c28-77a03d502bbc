import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtGui import QIcon
from pdf_to_excel_gui import PdfToExcelApp

# 改进对打包环境的检测
def get_application_path():
    if getattr(sys, 'frozen', False):
        # 运行在打包环境中
        return sys._MEIPASS
    else:
        # 运行在开发环境中
        return os.path.dirname(os.path.abspath(__file__))

if __name__ == '__main__':
    try:
        # 首先设置Windows应用程序ID（必须在创建QApplication之前）
        if sys.platform == 'win32':
            try:
                from ctypes import windll
                # 使用更明确的应用程序ID，应根据实际应用调整
                app_id = 'PDFtoEXCEL.PySide6.Application.1.0'
                windll.shell32.SetCurrentProcessExplicitAppUserModelID(app_id)
                print(f"已设置Windows应用ID: {app_id}")
            except Exception as e:
                print(f"设置Windows应用ID时出错: {e}")
        
        # 创建应用实例
        app = QApplication(sys.argv)
        
        # 获取应用程序路径
        app_path = get_application_path()
        icon_path = os.path.join(app_path, "icon.ico")
        
        if os.path.exists(icon_path):
            # 创建QIcon对象
            app_icon = QIcon(icon_path)
            # 设置应用程序图标
            app.setWindowIcon(app_icon)
            print(f"应用图标已设置: {icon_path}")
        else:
            print(f"图标文件未找到: {icon_path}")
        
        # 创建并显示主窗口
        window = PdfToExcelApp()
        # 为窗口也设置相同的图标，确保一致性
        if os.path.exists(icon_path):
            window.setWindowIcon(QIcon(icon_path))
        window.show()
        
        # 运行应用
        sys.exit(app.exec())
    except ImportError as e:
        print(f"导入错误: {e}")
        try:
            error_msg_box = QMessageBox()
            error_msg_box.setIcon(QMessageBox.Critical)
            error_msg_box.setText("应用启动失败")
            error_msg_box.setInformativeText(f"导入错误: {e}\n请确保已安装所有必要依赖。")
            error_msg_box.setWindowTitle("启动错误")
            error_msg_box.exec()
        except:
            pass
        sys.exit(1)
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        try:
            error_msg_box = QMessageBox()
            error_msg_box.setIcon(QMessageBox.Critical)
            error_msg_box.setText("应用程序启动失败")
            error_msg_box.setInformativeText(f"错误: {e}\n请检查依赖项。")
            error_msg_box.setWindowTitle("启动错误")
            error_msg_box.exec()
        except:
            pass
        sys.exit(1) 