"""
OCR Engine Base Classes
=======================

Abstract base classes for OCR engines providing a unified interface.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Union
import numpy as np
from pathlib import Path

from ...domain.models import OCRResult, LayoutResult, TableResult, SealResult, TextResult


class OCREngineBase(ABC):
    """Abstract base class for OCR engines."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self._initialized = False

    @abstractmethod
    def initialize(self) -> None:
        """Initialize the OCR engine."""
        pass

    @abstractmethod
    def process_page(self, image: np.ndarray, page_number: int) -> OCRResult:
        """Process a single page image and return OCR results."""
        pass

    @abstractmethod
    def cleanup(self) -> None:
        """Clean up resources."""
        pass

    @property
    def is_initialized(self) -> bool:
        """Check if the engine is initialized."""
        return self._initialized

    @property
    @abstractmethod
    def engine_name(self) -> str:
        """Get the engine name."""
        pass

    @property
    @abstractmethod
    def supported_features(self) -> List[str]:
        """Get list of supported features."""
        pass


class LayoutDetectionEngine(ABC):
    """Abstract base class for layout detection engines."""

    @abstractmethod
    def detect_layout(self, image: np.ndarray) -> LayoutResult:
        """Detect layout elements in an image."""
        pass

    @property
    @abstractmethod
    def supported_region_types(self) -> List[str]:
        """Get list of supported region types."""
        pass


class TextRecognitionEngine(ABC):
    """Abstract base class for text recognition engines."""

    @abstractmethod
    def recognize_text(self, image: np.ndarray, regions: Optional[List[Dict[str, Any]]] = None) -> List[TextResult]:
        """Recognize text in an image or specific regions."""
        pass

    @property
    @abstractmethod
    def supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        pass


class TableRecognitionEngine(ABC):
    """Abstract base class for table recognition engines."""

    @abstractmethod
    def recognize_tables(self, image: np.ndarray, table_regions: List[Dict[str, Any]]) -> List[TableResult]:
        """Recognize tables in specified regions."""
        pass

    @property
    @abstractmethod
    def output_formats(self) -> List[str]:
        """Get list of supported output formats."""
        pass


class SealRecognitionEngine(ABC):
    """Abstract base class for seal recognition engines."""

    @abstractmethod
    def recognize_seals(self, image: np.ndarray, seal_regions: List[Dict[str, Any]]) -> List[SealResult]:
        """Recognize seals in specified regions."""
        pass

    @property
    @abstractmethod
    def seal_types(self) -> List[str]:
        """Get list of supported seal types."""
        pass


class CompositeOCREngine(OCREngineBase):
    """Composite OCR engine that combines multiple specialized engines."""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.layout_engine: Optional[LayoutDetectionEngine] = None
        self.text_engine: Optional[TextRecognitionEngine] = None
        self.table_engine: Optional[TableRecognitionEngine] = None
        self.seal_engine: Optional[SealRecognitionEngine] = None

    def set_layout_engine(self, engine: LayoutDetectionEngine) -> None:
        """Set the layout detection engine."""
        self.layout_engine = engine

    def set_text_engine(self, engine: TextRecognitionEngine) -> None:
        """Set the text recognition engine."""
        self.text_engine = engine

    def set_table_engine(self, engine: TableRecognitionEngine) -> None:
        """Set the table recognition engine."""
        self.table_engine = engine

    def set_seal_engine(self, engine: SealRecognitionEngine) -> None:
        """Set the seal recognition engine."""
        self.seal_engine = engine

    def process_page(self, image: np.ndarray, page_number: int) -> OCRResult:
        """Process a page using all available engines."""
        if not self.is_initialized:
            raise RuntimeError("Engine not initialized")

        result = OCRResult(page_number=page_number)

        # Step 1: Layout detection
        if self.layout_engine:
            result.layout_result = self.layout_engine.detect_layout(image)

            # Step 2: Text recognition
            if self.text_engine:
                text_regions = result.layout_result.get_text_regions()
                result.text_results = self.text_engine.recognize_text(image, text_regions)

            # Step 3: Table recognition
            if self.table_engine:
                table_regions = result.layout_result.get_table_regions()
                if table_regions:
                    result.table_results = self.table_engine.recognize_tables(image, table_regions)

            # Step 4: Seal recognition
            if self.seal_engine:
                seal_regions = result.layout_result.get_seal_regions()
                if seal_regions:
                    result.seal_results = self.seal_engine.recognize_seals(image, seal_regions)

        else:
            # Fallback: process entire image with available engines
            if self.text_engine:
                result.text_results = self.text_engine.recognize_text(image)

        return result

    @property
    def engine_name(self) -> str:
        """Get the composite engine name."""
        return "CompositeOCREngine"

    @property
    def supported_features(self) -> List[str]:
        """Get list of supported features from all engines."""
        features = []

        if self.layout_engine:
            features.append("layout_detection")
        if self.text_engine:
            features.append("text_recognition")
        if self.table_engine:
            features.append("table_recognition")
        if self.seal_engine:
            features.append("seal_recognition")

        return features

    def initialize(self) -> None:
        """Initialize all engines."""
        # Individual engines should be initialized separately
        self._initialized = True

    def cleanup(self) -> None:
        """Clean up all engines."""
        # Individual engines should be cleaned up separately
        self._initialized = False