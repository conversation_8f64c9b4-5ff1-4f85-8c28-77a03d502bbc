"""
OCR Result Domain Models
========================

Domain models representing OCR processing results from different engines.
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
import pandas as pd
import numpy as np
from .document import BoundingBox


@dataclass
class TextResult:
    """Represents text recognition result."""
    text: str
    confidence: float
    bounding_box: BoundingBox
    language: Optional[str] = None
    font_info: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        """Post-initialization validation."""
        if not 0 <= self.confidence <= 1:
            raise ValueError("Confidence must be between 0 and 1")


@dataclass
class LayoutResult:
    """Represents layout detection result."""
    regions: List[Dict[str, Any]]
    page_width: float
    page_height: float
    processing_time: float = 0.0
    model_name: str = ""

    def get_regions_by_type(self, region_type: str) -> List[Dict[str, Any]]:
        """Get regions of a specific type."""
        return [region for region in self.regions if region.get('type') == region_type]

    def get_table_regions(self) -> List[Dict[str, Any]]:
        """Get all table regions."""
        return self.get_regions_by_type('table')

    def get_text_regions(self) -> List[Dict[str, Any]]:
        """Get all text regions."""
        return self.get_regions_by_type('text')

    def get_seal_regions(self) -> List[Dict[str, Any]]:
        """Get all seal regions."""
        return self.get_regions_by_type('seal')


@dataclass
class TableCell:
    """Represents a single table cell."""
    row: int
    col: int
    text: str
    confidence: float
    bounding_box: Optional[BoundingBox] = None
    rowspan: int = 1
    colspan: int = 1

    def __post_init__(self):
        """Post-initialization validation."""
        if self.row < 0 or self.col < 0:
            raise ValueError("Row and column indices must be non-negative")
        if self.rowspan < 1 or self.colspan < 1:
            raise ValueError("Rowspan and colspan must be at least 1")


@dataclass
class TableResult:
    """Represents table recognition result."""
    cells: List[TableCell]
    table_bbox: BoundingBox
    confidence: float
    html_structure: Optional[str] = None
    processing_time: float = 0.0
    model_name: str = ""

    def __post_init__(self):
        """Post-initialization validation."""
        if not 0 <= self.confidence <= 1:
            raise ValueError("Confidence must be between 0 and 1")

    def to_dataframe(self) -> pd.DataFrame:
        """Convert table result to pandas DataFrame."""
        if not self.cells:
            return pd.DataFrame()

        # Determine table dimensions
        max_row = max(cell.row + cell.rowspan - 1 for cell in self.cells)
        max_col = max(cell.col + cell.colspan - 1 for cell in self.cells)

        # Create empty table
        table_data = [["" for _ in range(max_col + 1)] for _ in range(max_row + 1)]

        # Fill table with cell data
        for cell in self.cells:
            for r in range(cell.row, cell.row + cell.rowspan):
                for c in range(cell.col, cell.col + cell.colspan):
                    if r <= max_row and c <= max_col:
                        table_data[r][c] = cell.text

        return pd.DataFrame(table_data)

    def get_cell_at(self, row: int, col: int) -> Optional[TableCell]:
        """Get cell at specific row and column."""
        for cell in self.cells:
            if (cell.row <= row < cell.row + cell.rowspan and
                cell.col <= col < cell.col + cell.colspan):
                return cell
        return None

    @property
    def row_count(self) -> int:
        """Get the number of rows in the table."""
        if not self.cells:
            return 0
        return max(cell.row + cell.rowspan for cell in self.cells)

    @property
    def col_count(self) -> int:
        """Get the number of columns in the table."""
        if not self.cells:
            return 0
        return max(cell.col + cell.colspan for cell in self.cells)


@dataclass
class SealResult:
    """Represents seal text recognition result."""
    seal_bbox: BoundingBox
    text_results: List[TextResult]
    confidence: float
    processing_time: float = 0.0
    model_name: str = ""

    def __post_init__(self):
        """Post-initialization validation."""
        if not 0 <= self.confidence <= 1:
            raise ValueError("Confidence must be between 0 and 1")

    @property
    def combined_text(self) -> str:
        """Get all text results combined."""
        return " ".join(result.text for result in self.text_results)

    @property
    def average_confidence(self) -> float:
        """Get average confidence of all text results."""
        if not self.text_results:
            return self.confidence
        return sum(result.confidence for result in self.text_results) / len(self.text_results)


@dataclass
class OCRResult:
    """Comprehensive OCR result containing all processing outputs."""
    page_number: int
    layout_result: Optional[LayoutResult] = None
    text_results: List[TextResult] = field(default_factory=list)
    table_results: List[TableResult] = field(default_factory=list)
    seal_results: List[SealResult] = field(default_factory=list)
    processing_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Post-initialization validation."""
        if self.page_number < 1:
            raise ValueError("Page number must be positive")

    def get_all_text(self) -> str:
        """Get all recognized text combined."""
        all_text = []

        # Add text results
        for text_result in self.text_results:
            all_text.append(text_result.text)

        # Add table text
        for table_result in self.table_results:
            for cell in table_result.cells:
                if cell.text.strip():
                    all_text.append(cell.text)

        # Add seal text
        for seal_result in self.seal_results:
            all_text.append(seal_result.combined_text)

        return " ".join(all_text)

    def get_tables_as_dataframes(self) -> List[pd.DataFrame]:
        """Get all tables as pandas DataFrames."""
        return [table.to_dataframe() for table in self.table_results]

    @property
    def has_tables(self) -> bool:
        """Check if the result contains any tables."""
        return len(self.table_results) > 0

    @property
    def has_seals(self) -> bool:
        """Check if the result contains any seals."""
        return len(self.seal_results) > 0

    @property
    def total_confidence(self) -> float:
        """Calculate overall confidence score."""
        confidences = []

        # Add text confidences
        confidences.extend([result.confidence for result in self.text_results])

        # Add table confidences
        confidences.extend([result.confidence for result in self.table_results])

        # Add seal confidences
        confidences.extend([result.confidence for result in self.seal_results])

        if not confidences:
            return 0.0

        return sum(confidences) / len(confidences)