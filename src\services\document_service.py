"""
Document Processing Service
===========================

Service for handling PDF document loading, preprocessing, and page extraction.
"""

import time
from typing import List, Optional, Dict, Any, Generator
from pathlib import Path
import numpy as np
import cv2

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

try:
    import pdf2image
    PDF2IMAGE_AVAILABLE = True
except ImportError:
    PDF2IMAGE_AVAILABLE = False

from ..domain.models import Document, Page, BoundingBox
from ..core.logging import get_logger
from ..core.events import publish_event, ProcessingStartedEvent, ProcessingProgressEvent

logger = get_logger(__name__)


class DocumentService:
    """Service for document processing operations."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        temp_dir_path = config.get('temp_dir') or 'temp'
        self.temp_dir = Path(temp_dir_path)
        self.temp_dir.mkdir(exist_ok=True)

    def load_document(self, file_path: Path) -> Document:
        """Load a PDF document and create a Document object."""
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        if file_path.suffix.lower() != '.pdf':
            raise ValueError(f"Unsupported file format: {file_path.suffix}")

        logger.info(f"Loading document: {file_path}")

        # Create document object
        document = Document(file_path=file_path)

        # Get document metadata
        metadata = self._extract_pdf_metadata(file_path)
        document.metadata.update(metadata)

        logger.info(f"Document loaded successfully: {document.total_pages} pages")
        return document

    def extract_pages_as_images(
        self,
        document: Document,
        dpi: int = 200,
        pages: Optional[List[int]] = None
    ) -> Generator[tuple[int, np.ndarray], None, None]:
        """Extract PDF pages as images."""
        if not PYMUPDF_AVAILABLE and not PDF2IMAGE_AVAILABLE:
            raise RuntimeError("Neither PyMuPDF nor pdf2image is available. Please install one of them.")

        total_pages = self._get_page_count(document.file_path)
        pages_to_process = pages if pages else list(range(1, total_pages + 1))

        # Publish processing started event
        publish_event(ProcessingStartedEvent(
            file_path=str(document.file_path),
            total_pages=len(pages_to_process),
            source="DocumentService"
        ))

        logger.info(f"Extracting {len(pages_to_process)} pages as images (DPI: {dpi})")

        if PYMUPDF_AVAILABLE:
            yield from self._extract_pages_pymupdf(document.file_path, pages_to_process, dpi)
        else:
            yield from self._extract_pages_pdf2image(document.file_path, pages_to_process, dpi)

    def _extract_pages_pymupdf(
        self,
        pdf_path: Path,
        pages: List[int],
        dpi: int
    ) -> Generator[tuple[int, np.ndarray], None, None]:
        """Extract pages using PyMuPDF."""
        try:
            doc = fitz.open(str(pdf_path))

            for i, page_num in enumerate(pages):
                try:
                    # Get page (0-indexed)
                    page = doc[page_num - 1]

                    # Convert to image
                    mat = fitz.Matrix(dpi / 72, dpi / 72)  # Scale factor for DPI
                    pix = page.get_pixmap(matrix=mat)

                    # Convert to numpy array
                    img_data = pix.tobytes("ppm")
                    nparr = np.frombuffer(img_data, np.uint8)
                    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

                    # Convert BGR to RGB
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                    # Publish progress
                    progress = (i + 1) / len(pages) * 100
                    publish_event(ProcessingProgressEvent(
                        current_step=f"Extracting page {page_num}",
                        progress_percentage=progress,
                        source="DocumentService"
                    ))

                    yield page_num, image

                except Exception as e:
                    logger.error(f"Failed to extract page {page_num}: {e}")
                    continue

            doc.close()

        except Exception as e:
            logger.error(f"Failed to open PDF with PyMuPDF: {e}")
            raise

    def _extract_pages_pdf2image(
        self,
        pdf_path: Path,
        pages: List[int],
        dpi: int
    ) -> Generator[tuple[int, np.ndarray], None, None]:
        """Extract pages using pdf2image."""
        try:
            from pdf2image import convert_from_path

            # Convert specific pages
            images = convert_from_path(
                str(pdf_path),
                dpi=dpi,
                first_page=min(pages),
                last_page=max(pages)
            )

            for i, (page_num, pil_image) in enumerate(zip(pages, images)):
                try:
                    # Convert PIL image to numpy array
                    image = np.array(pil_image)

                    # Publish progress
                    progress = (i + 1) / len(pages) * 100
                    publish_event(ProcessingProgressEvent(
                        current_step=f"Extracting page {page_num}",
                        progress_percentage=progress,
                        source="DocumentService"
                    ))

                    yield page_num, image

                except Exception as e:
                    logger.error(f"Failed to process page {page_num}: {e}")
                    continue

        except Exception as e:
            logger.error(f"Failed to extract pages with pdf2image: {e}")
            raise

    def preprocess_image(self, image: np.ndarray, config: Optional[Dict[str, Any]] = None) -> np.ndarray:
        """Preprocess image for better OCR results."""
        if config is None:
            config = self.config.get('preprocessing', {})

        processed_image = image.copy()

        # Apply preprocessing steps
        if config.get('denoise', False):
            processed_image = cv2.fastNlMeansDenoisingColored(processed_image)

        if config.get('enhance_contrast', False):
            lab = cv2.cvtColor(processed_image, cv2.COLOR_RGB2LAB)
            l, a, b = cv2.split(lab)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            l = clahe.apply(l)
            processed_image = cv2.merge([l, a, b])
            processed_image = cv2.cvtColor(processed_image, cv2.COLOR_LAB2RGB)

        if config.get('sharpen', False):
            kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
            processed_image = cv2.filter2D(processed_image, -1, kernel)

        return processed_image

    def _extract_pdf_metadata(self, pdf_path: Path) -> Dict[str, Any]:
        """Extract metadata from PDF file."""
        metadata = {}

        try:
            if PYMUPDF_AVAILABLE:
                doc = fitz.open(str(pdf_path))
                pdf_metadata = doc.metadata

                metadata.update({
                    'title': pdf_metadata.get('title', ''),
                    'author': pdf_metadata.get('author', ''),
                    'subject': pdf_metadata.get('subject', ''),
                    'creator': pdf_metadata.get('creator', ''),
                    'producer': pdf_metadata.get('producer', ''),
                    'creation_date': pdf_metadata.get('creationDate', ''),
                    'modification_date': pdf_metadata.get('modDate', ''),
                    'page_count': doc.page_count
                })

                doc.close()

        except Exception as e:
            logger.warning(f"Failed to extract PDF metadata: {e}")
            metadata['page_count'] = self._get_page_count(pdf_path)

        return metadata

    def _get_page_count(self, pdf_path: Path) -> int:
        """Get the number of pages in a PDF file."""
        try:
            if PYMUPDF_AVAILABLE:
                doc = fitz.open(str(pdf_path))
                count = doc.page_count
                doc.close()
                return count
        except Exception as e:
            logger.warning(f"Failed to get page count with PyMuPDF: {e}")

        try:
            if PDF2IMAGE_AVAILABLE:
                from pdf2image import pdfinfo_from_path
                info = pdfinfo_from_path(str(pdf_path))
                return info.get('Pages', 0)
        except Exception as e:
            logger.warning(f"Failed to get page count with pdf2image: {e}")

        return 0

    def create_page_object(self, page_number: int, image: np.ndarray) -> Page:
        """Create a Page object from image data."""
        height, width = image.shape[:2]

        page = Page(
            page_number=page_number,
            width=float(width),
            height=float(height),
            image_data=image
        )

        return page

    def cleanup_temp_files(self) -> None:
        """Clean up temporary files."""
        try:
            if self.temp_dir.exists():
                for file in self.temp_dir.glob('*'):
                    if file.is_file():
                        file.unlink()
                logger.info("Temporary files cleaned up")
        except Exception as e:
            logger.warning(f"Failed to clean up temporary files: {e}")