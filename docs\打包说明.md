# PDF表格转Excel工具 - 打包指南

本文档提供将应用程序打包为Windows可执行文件的详细步骤。

## 准备工作

在开始打包前，请确保：

1. 已安装Python 3.7+
2. 已安装所有必需依赖（运行 `pip install -r requirements.txt`）
3. 应用程序能正常运行（运行 `python app_with_icon.py` 测试）
4. 图标文件 `icon.ico` 位于项目根目录下

## 打包步骤

### 方法一：使用spec文件打包（推荐）

1. **运行打包脚本**
   - 双击运行 `build_with_spec.bat`

2. **配置打包选项**
   - 是否包含PaddleOCR功能：
     - 选择 `1`：将包含OCR功能，但会显著增加文件大小
     - 选择 `2`（默认）：不包含OCR功能，减小文件大小
   
   - 是否打包为单个EXE文件：
     - 选择 `1`：生成单个EXE文件，方便分发
     - 选择 `2`（默认）：生成文件夹，启动速度更快

3. **等待打包完成**
   - 打包过程可能需要几分钟时间，具体取决于您的计算机性能。
   - 完成后，打包好的程序将位于 `dist` 文件夹中。

### 方法二：使用批处理脚本

1. **运行批处理脚本**
   - 双击运行 `build_exe.bat`

2. **配置打包选项**
   - 是否包含PaddleOCR功能：
     - 选择 `y`：将包含OCR功能，但会显著增加文件大小
     - 选择 `n`（默认）：不包含OCR功能，减小文件大小
   
   - 是否打包为单个EXE文件：
     - 选择 `y`：生成单个EXE文件，方便分发
     - 选择 `n`（默认）：生成文件夹，启动速度更快

### 方法三：手动使用PyInstaller

如果批处理脚本无法工作，您也可以手动运行PyInstaller命令：

1. **安装PyInstaller**：
   ```
   pip install pyinstaller
   ```

2. **打包应用（使用spec文件）**：
   ```
   # 不包含OCR功能
   pyinstaller --exclude-paddle pdf_to_excel.spec
   
   # 包含OCR功能
   pyinstaller pdf_to_excel.spec
   
   # 打包为单文件(不含OCR)
   pyinstaller --exclude-paddle --onefile pdf_to_excel.spec
   ```

3. **直接打包应用（不使用spec文件）**：
   ```
   # 不含OCR功能
   pyinstaller --name "PDF表格转Excel工具" --icon icon.ico --noconsole --add-data "icon.ico;." --onedir --exclude-module paddleocr --exclude-module paddlepaddle app_with_icon.py
   
   # 包含OCR功能
   pyinstaller --name "PDF表格转Excel工具" --icon icon.ico --noconsole --add-data "icon.ico;." --onedir app_with_icon.py
   ```

## Windows编码问题解决

在Windows系统上，如果遇到`UnicodeDecodeError: 'gbk' codec can't decode byte`错误，这通常是因为文件包含非ASCII字符，且Windows默认使用GBK编码。解决方法：

1. **使用UTF-8编码保存所有文件**
   - 确保所有Python文件和文本文件使用UTF-8编码保存
   - 使用`# -*- coding: utf-8 -*-`作为文件第一行

2. **在requirements.txt中避免中文注释**
   - 使用英文注释替代中文注释
   - 或者使用`pip install`命令单独安装依赖

## 打包结果说明

打包完成后：

- **文件夹模式**：
  - 在 `dist/PDF表格转Excel工具/` 文件夹中找到 `PDF表格转Excel工具.exe`
  - 需要分发整个文件夹
  - 启动速度较快

- **单文件模式**：
  - 在 `dist/` 文件夹中找到 `PDF表格转Excel工具.exe`
  - 只需分发这一个EXE文件
  - 启动速度较慢（需要解压缩）

## 常见问题

### 1. 找不到图标文件

确保 `icon.ico` 文件位于项目根目录下。如果没有，您可以使用任何ICO格式图标，并将其命名为 `icon.ico`。

### 2. 安装PyInstaller失败

手动安装PyInstaller并检查错误信息：
```
pip install pyinstaller
```

### 3. 导入错误

如果打包过程中遇到导入错误，通常是因为：
- 未安装某些依赖：确保已安装所有依赖 `pip install -r requirements.txt`
- 模块名称错误：检查您的import语句
- Windows路径问题：确保无中文或特殊字符路径

### 4. 应用无法启动

可能的原因：
- 应用依赖的DLL或库文件缺失
- 系统上未安装依赖程序（如Ghostscript）
- 权限问题

解决方案：
- 尝试使用文件夹模式（`--onedir`）而非单文件模式
- 运行命令行版本检查错误信息：`--debug all`
- 确保已安装所有外部依赖（如Ghostscript）

### 5. 打包文件太大

如果不需要OCR功能，请确保在打包时排除PaddleOCR相关模块：
- 使用`--exclude-paddle`选项（spec文件打包）
- 选择不包含PaddleOCR功能选项（批处理脚本）

## 分发指南

分发应用程序时，请注意：

1. **文件夹模式**：
   - 分发整个 `dist/PDF表格转Excel工具/` 文件夹
   - 用户只需点击其中的EXE文件运行

2. **单文件模式**：
   - 只分发 `dist/PDF表格转Excel工具.exe` 文件
   - 可能还需要单独安装某些依赖（如Ghostscript）

3. **添加说明文件**：
   - 推荐包含README.md文件，说明如何使用
   - 对于有外部依赖的应用，提供INSTALL.md说明如何安装

4. **关于Ghostscript**：
   - Camelot依赖Ghostscript处理PDF文件
   - 即使是打包的应用也需要用户安装Ghostscript
   - 在分发说明中提醒用户安装Ghostscript 