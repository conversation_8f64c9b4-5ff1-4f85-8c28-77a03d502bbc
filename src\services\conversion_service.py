"""
Conversion Service
==================

Service for converting OCR results to various output formats (Excel, CSV, JSON).
"""

import time
from typing import List, Optional, Dict, Any, Union
from pathlib import Path
import pandas as pd
import json
from datetime import datetime

from ..domain.models import Document, Page, Region, TableResult, OCRResult
from ..core.logging import get_logger
from ..core.events import publish_event, ProcessingProgressEvent, ProcessingCompletedEvent

logger = get_logger(__name__)


class ConversionService:
    """Service for converting OCR results to various output formats."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.default_format = config.get('default_output_format', 'xlsx')
        self.output_dir = Path(config.get('output_dir', 'output'))
        self.output_dir.mkdir(exist_ok=True)

    def convert_document(
        self,
        document: Document,
        output_format: str = None,
        output_path: Optional[Path] = None
    ) -> Path:
        """Convert document to specified format."""
        format_type = output_format or self.default_format

        if not output_path:
            output_path = self._generate_output_path(document.file_path, format_type)

        logger.info(f"Converting document to {format_type}: {output_path}")

        # Publish conversion started event
        publish_event(ProcessingProgressEvent(
            current_step=f"Converting to {format_type}",
            progress_percentage=0.0,
            source="ConversionService"
        ))

        try:
            if format_type.lower() in ['xlsx', 'xls']:
                result_path = self._convert_to_excel(document, output_path)
            elif format_type.lower() == 'csv':
                result_path = self._convert_to_csv(document, output_path)
            elif format_type.lower() == 'json':
                result_path = self._convert_to_json(document, output_path)
            elif format_type.lower() == 'html':
                result_path = self._convert_to_html(document, output_path)
            else:
                raise ValueError(f"Unsupported output format: {format_type}")

            # Publish completion event
            publish_event(ProcessingCompletedEvent(
                file_path=str(document.file_path),
                output_path=str(result_path),
                success=True,
                source="ConversionService"
            ))

            logger.info(f"Document converted successfully: {result_path}")
            return result_path

        except Exception as e:
            logger.error(f"Conversion failed: {e}")

            # Publish failure event
            publish_event(ProcessingCompletedEvent(
                file_path=str(document.file_path),
                output_path=str(output_path),
                success=False,
                error_message=str(e),
                source="ConversionService"
            ))
            raise

    def _convert_to_excel(self, document: Document, output_path: Path) -> Path:
        """Convert document to Excel format."""
        with pd.ExcelWriter(str(output_path), engine='openpyxl') as writer:

            # Create summary sheet
            summary_data = self._create_summary_data(document)
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)

            # Process each page
            for i, page in enumerate(document.pages):
                sheet_name = f'Page_{page.page_number}'

                # Get all tables from the page
                table_regions = page.get_regions_by_type('table')

                if table_regions:
                    # Combine all tables from the page
                    combined_df = self._combine_page_tables(table_regions)
                    if not combined_df.empty:
                        combined_df.to_excel(writer, sheet_name=sheet_name, index=False)
                else:
                    # Create a sheet with text content if no tables
                    text_content = self._extract_page_text(page)
                    if text_content:
                        text_df = pd.DataFrame({'Content': text_content})
                        text_df.to_excel(writer, sheet_name=sheet_name, index=False)

                # Update progress
                progress = (i + 1) / len(document.pages) * 100
                publish_event(ProcessingProgressEvent(
                    current_step=f"Converting page {page.page_number}",
                    progress_percentage=progress,
                    source="ConversionService"
                ))

        return output_path

    def _convert_to_csv(self, document: Document, output_path: Path) -> Path:
        """Convert document to CSV format."""
        all_tables = []

        # Collect all tables from all pages
        for page in document.pages:
            table_regions = page.get_regions_by_type('table')
            for region in table_regions:
                if isinstance(region.content, TableResult):
                    table_df = region.content.to_dataframe()
                    table_df['Page'] = page.page_number
                    table_df['Table_ID'] = region.region_id
                    all_tables.append(table_df)

        if all_tables:
            # Combine all tables
            combined_df = pd.concat(all_tables, ignore_index=True)
            combined_df.to_csv(str(output_path), index=False, encoding='utf-8-sig')
        else:
            # Create CSV with text content
            text_data = []
            for page in document.pages:
                text_content = self._extract_page_text(page)
                for text in text_content:
                    text_data.append({
                        'Page': page.page_number,
                        'Content': text
                    })

            if text_data:
                text_df = pd.DataFrame(text_data)
                text_df.to_csv(str(output_path), index=False, encoding='utf-8-sig')

        return output_path

    def _convert_to_json(self, document: Document, output_path: Path) -> Path:
        """Convert document to JSON format."""
        json_data = {
            'document_info': {
                'file_name': document.file_name,
                'file_path': str(document.file_path),
                'total_pages': document.total_pages,
                'total_regions': document.total_regions,
                'created_at': document.created_at.isoformat(),
                'metadata': document.metadata
            },
            'pages': []
        }

        for page in document.pages:
            page_data = {
                'page_number': page.page_number,
                'width': page.width,
                'height': page.height,
                'regions': []
            }

            for region in page.regions:
                region_data = {
                    'region_id': region.region_id,
                    'region_type': region.region_type,
                    'bounding_box': {
                        'x1': region.bounding_box.x1,
                        'y1': region.bounding_box.y1,
                        'x2': region.bounding_box.x2,
                        'y2': region.bounding_box.y2
                    },
                    'confidence': region.confidence,
                    'content': self._serialize_region_content(region),
                    'metadata': region.metadata
                }
                page_data['regions'].append(region_data)

            json_data['pages'].append(page_data)

        # Write JSON file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)

        return output_path

    def _convert_to_html(self, document: Document, output_path: Path) -> Path:
        """Convert document to HTML format."""
        html_content = self._generate_html_content(document)

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        return output_path

    def _create_summary_data(self, document: Document) -> List[Dict[str, Any]]:
        """Create summary data for the document."""
        summary = []

        # Document overview
        summary.append({
            'Property': 'File Name',
            'Value': document.file_name
        })
        summary.append({
            'Property': 'Total Pages',
            'Value': document.total_pages
        })
        summary.append({
            'Property': 'Total Regions',
            'Value': document.total_regions
        })

        # Count regions by type
        region_counts = {}
        for page in document.pages:
            for region in page.regions:
                region_type = region.region_type
                region_counts[region_type] = region_counts.get(region_type, 0) + 1

        for region_type, count in region_counts.items():
            summary.append({
                'Property': f'{region_type.title()} Regions',
                'Value': count
            })

        # Processing timestamp
        summary.append({
            'Property': 'Processed At',
            'Value': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

        return summary

    def _combine_page_tables(self, table_regions: List[Region]) -> pd.DataFrame:
        """Combine all tables from a page into a single DataFrame."""
        combined_tables = []

        for region in table_regions:
            if isinstance(region.content, TableResult):
                table_df = region.content.to_dataframe()
                if not table_df.empty:
                    # Add metadata columns
                    table_df['Table_ID'] = region.region_id
                    table_df['Confidence'] = region.confidence
                    combined_tables.append(table_df)

        if combined_tables:
            return pd.concat(combined_tables, ignore_index=True)
        else:
            return pd.DataFrame()

    def _extract_page_text(self, page: Page) -> List[str]:
        """Extract all text content from a page."""
        text_content = []

        # Get text regions
        text_regions = page.get_regions_by_type('text')
        for region in text_regions:
            if isinstance(region.content, str):
                text_content.append(region.content)

        # Get seal text
        seal_regions = page.get_regions_by_type('seal')
        for region in seal_regions:
            if hasattr(region.content, 'combined_text'):
                text_content.append(region.content.combined_text)

        return text_content

    def _serialize_region_content(self, region: Region) -> Any:
        """Serialize region content for JSON output."""
        if isinstance(region.content, str):
            return region.content
        elif isinstance(region.content, TableResult):
            return {
                'type': 'table',
                'cells': [
                    {
                        'row': cell.row,
                        'col': cell.col,
                        'text': cell.text,
                        'confidence': cell.confidence
                    }
                    for cell in region.content.cells
                ],
                'html_structure': region.content.html_structure
            }
        elif hasattr(region.content, 'combined_text'):  # SealResult
            return {
                'type': 'seal',
                'text': region.content.combined_text,
                'confidence': region.content.confidence
            }
        else:
            return str(region.content) if region.content else None

    def _generate_html_content(self, document: Document) -> str:
        """Generate HTML content for the document."""
        html_parts = [
            '<!DOCTYPE html>',
            '<html lang="zh-CN">',
            '<head>',
            '    <meta charset="UTF-8">',
            '    <meta name="viewport" content="width=device-width, initial-scale=1.0">',
            f'    <title>{document.file_name} - OCR Results</title>',
            '    <style>',
            '        body { font-family: Arial, sans-serif; margin: 20px; }',
            '        .page { margin-bottom: 30px; border: 1px solid #ccc; padding: 20px; }',
            '        .page-header { background-color: #f5f5f5; padding: 10px; margin-bottom: 15px; }',
            '        .region { margin-bottom: 15px; padding: 10px; border-left: 3px solid #007acc; }',
            '        .text-region { background-color: #f9f9f9; }',
            '        .table-region { background-color: #fff5f5; }',
            '        .seal-region { background-color: #f5fff5; }',
            '        table { border-collapse: collapse; width: 100%; margin-top: 10px; }',
            '        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }',
            '        th { background-color: #f2f2f2; }',
            '        .confidence { font-size: 0.8em; color: #666; }',
            '    </style>',
            '</head>',
            '<body>',
            f'    <h1>OCR Results: {document.file_name}</h1>',
            f'    <p>Total Pages: {document.total_pages} | Total Regions: {document.total_regions}</p>'
        ]

        # Add pages
        for page in document.pages:
            html_parts.extend([
                f'    <div class="page">',
                f'        <div class="page-header">',
                f'            <h2>Page {page.page_number}</h2>',
                f'            <p>Dimensions: {page.width:.0f} x {page.height:.0f} | Regions: {len(page.regions)}</p>',
                f'        </div>'
            ])

            # Add regions
            for region in page.regions:
                region_class = f"{region.region_type}-region"
                html_parts.extend([
                    f'        <div class="region {region_class}">',
                    f'            <h3>{region.region_type.title()} Region ({region.region_id})</h3>',
                    f'            <div class="confidence">Confidence: {region.confidence:.2%}</div>'
                ])

                # Add region content
                if region.region_type == 'table' and isinstance(region.content, TableResult):
                    table_df = region.content.to_dataframe()
                    if not table_df.empty:
                        html_parts.append('            <table>')
                        # Add header
                        html_parts.append('                <tr>')
                        for col in table_df.columns:
                            html_parts.append(f'                    <th>{col}</th>')
                        html_parts.append('                </tr>')
                        # Add rows
                        for _, row in table_df.iterrows():
                            html_parts.append('                <tr>')
                            for value in row:
                                html_parts.append(f'                    <td>{value}</td>')
                            html_parts.append('                </tr>')
                        html_parts.append('            </table>')
                else:
                    content = self._serialize_region_content(region)
                    if isinstance(content, dict) and content.get('type') == 'seal':
                        html_parts.append(f'            <p><strong>Seal Text:</strong> {content["text"]}</p>')
                    elif isinstance(content, str):
                        html_parts.append(f'            <p>{content}</p>')

                html_parts.append('        </div>')

            html_parts.append('    </div>')

        html_parts.extend([
            '</body>',
            '</html>'
        ])

        return '\n'.join(html_parts)

    def _generate_output_path(self, input_path: Path, format_type: str) -> Path:
        """Generate output file path based on input path and format."""
        base_name = input_path.stem
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_name = f"{base_name}_ocr_{timestamp}.{format_type}"
        return self.output_dir / output_name

    def get_supported_formats(self) -> List[str]:
        """Get list of supported output formats."""
        return ['xlsx', 'xls', 'csv', 'json', 'html']

    def validate_format(self, format_type: str) -> bool:
        """Validate if the format is supported."""
        return format_type.lower() in self.get_supported_formats()