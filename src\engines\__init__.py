"""
OCR Engines Package
===================

Contains all OCR engine implementations and abstractions.
"""

from .base import *
from .paddleocr import *
from .camelot import *

__all__ = [
    # Base classes
    'OCREngineBase',
    'LayoutDetectionEngine',
    'TextRecognitionEngine',
    'TableRecognitionEngine',
    'SealRecognitionEngine',
    'CompositeOCREngine',

    # PaddleOCR engines
    'PaddleLayoutEngine',
    'PaddleTextEngine',
    'PaddleTableEngine',
    'PaddleSealEngine',
    'PaddleOCREngine',

    # Camelot engines
    'CamelotTableEngine',
    'CamelotPDFEngine',
]