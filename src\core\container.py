"""
Dependency Injection Container
==============================

A lightweight dependency injection container for managing service lifetimes and dependencies.
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Callable, Dict, Optional, Type, TypeVar, Union
import threading
import inspect

T = TypeVar('T')


class ServiceLifetime(Enum):
    """Service lifetime enumeration."""
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"


class ServiceDescriptor:
    """Describes a service registration."""

    def __init__(
        self,
        service_type: Type[T],
        implementation_type: Optional[Type[T]] = None,
        factory: Optional[Callable[..., T]] = None,
        instance: Optional[T] = None,
        lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT
    ):
        self.service_type = service_type
        self.implementation_type = implementation_type
        self.factory = factory
        self.instance = instance
        self.lifetime = lifetime

        if not any([implementation_type, factory, instance]):
            raise ValueError("Must provide implementation_type, factory, or instance")


class Container:
    """Dependency injection container."""

    def __init__(self):
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._singletons: Dict[Type, Any] = {}
        self._lock = threading.RLock()

    def register_singleton(
        self,
        service_type: Type[T],
        implementation_type: Optional[Type[T]] = None,
        factory: Optional[Callable[..., T]] = None,
        instance: Optional[T] = None
    ) -> 'Container':
        """Register a singleton service."""
        return self._register(
            service_type,
            implementation_type,
            factory,
            instance,
            ServiceLifetime.SINGLETON
        )

    def register_transient(
        self,
        service_type: Type[T],
        implementation_type: Optional[Type[T]] = None,
        factory: Optional[Callable[..., T]] = None
    ) -> 'Container':
        """Register a transient service."""
        return self._register(
            service_type,
            implementation_type,
            factory,
            None,
            ServiceLifetime.TRANSIENT
        )

    def register_scoped(
        self,
        service_type: Type[T],
        implementation_type: Optional[Type[T]] = None,
        factory: Optional[Callable[..., T]] = None
    ) -> 'Container':
        """Register a scoped service."""
        return self._register(
            service_type,
            implementation_type,
            factory,
            None,
            ServiceLifetime.SCOPED
        )

    def _register(
        self,
        service_type: Type[T],
        implementation_type: Optional[Type[T]],
        factory: Optional[Callable[..., T]],
        instance: Optional[T],
        lifetime: ServiceLifetime
    ) -> 'Container':
        """Internal registration method."""
        with self._lock:
            descriptor = ServiceDescriptor(
                service_type=service_type,
                implementation_type=implementation_type,
                factory=factory,
                instance=instance,
                lifetime=lifetime
            )
            self._services[service_type] = descriptor

            # If registering a singleton instance, store it immediately
            if lifetime == ServiceLifetime.SINGLETON and instance is not None:
                self._singletons[service_type] = instance

        return self

    def resolve(self, service_type: Type[T]) -> T:
        """Resolve a service instance."""
        with self._lock:
            if service_type not in self._services:
                raise ValueError(f"Service {service_type.__name__} is not registered")

            descriptor = self._services[service_type]

            # Handle singleton lifetime
            if descriptor.lifetime == ServiceLifetime.SINGLETON:
                if service_type in self._singletons:
                    return self._singletons[service_type]

                instance = self._create_instance(descriptor)
                self._singletons[service_type] = instance
                return instance

            # Handle transient and scoped lifetimes
            return self._create_instance(descriptor)

    def _create_instance(self, descriptor: ServiceDescriptor) -> Any:
        """Create an instance from a service descriptor."""
        # Use provided instance
        if descriptor.instance is not None:
            return descriptor.instance

        # Use factory function
        if descriptor.factory is not None:
            return self._invoke_factory(descriptor.factory)

        # Use implementation type
        if descriptor.implementation_type is not None:
            return self._create_from_type(descriptor.implementation_type)

        raise ValueError("Cannot create instance: no valid creation method")

    def _invoke_factory(self, factory: Callable[..., Any]) -> Any:
        """Invoke a factory function with dependency injection."""
        sig = inspect.signature(factory)
        kwargs = {}

        for param_name, param in sig.parameters.items():
            if param.annotation != inspect.Parameter.empty:
                kwargs[param_name] = self.resolve(param.annotation)

        return factory(**kwargs)

    def _create_from_type(self, implementation_type: Type[T]) -> T:
        """Create an instance from a type with dependency injection."""
        sig = inspect.signature(implementation_type.__init__)
        kwargs = {}

        for param_name, param in sig.parameters.items():
            if param_name == 'self':
                continue
            if param.annotation != inspect.Parameter.empty:
                kwargs[param_name] = self.resolve(param.annotation)

        return implementation_type(**kwargs)

    def is_registered(self, service_type: Type) -> bool:
        """Check if a service type is registered."""
        return service_type in self._services

    def clear(self):
        """Clear all registrations."""
        with self._lock:
            self._services.clear()
            self._singletons.clear()