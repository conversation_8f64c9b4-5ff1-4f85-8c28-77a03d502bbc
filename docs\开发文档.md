# PDF转Excel工具 - 开发者文档

## 🏗️ 架构概述

PDF转Excel工具采用现代化的分层架构设计，遵循SOLID原则和依赖注入模式，确保代码的可维护性、可测试性和可扩展性。

### 架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (UI Layer)                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   PySide6 GUI   │  │  命令行界面      │  │   Web界面       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   应用服务层 (App Layer)                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              PDFToExcelApp (主应用程序)                  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   业务服务层 (Service Layer)                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  DocumentService │  │   OCRService    │  │ConversionService│ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐                    │
│  │ ProgressService │  │  其他服务...     │                    │
│  └─────────────────┘  └─────────────────┘                    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   OCR引擎层 (Engine Layer)                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  PaddleOCR      │  │   Camelot       │  │   其他引擎...    │ │
│  │  PP-StructureV3 │  │   (备用)        │  │                │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   领域模型层 (Domain Layer)                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │    Document     │  │   OCRResult     │  │   其他模型...    │ │
│  │    Page         │  │   TextResult    │  │                │ │
│  │    Region       │  │   TableResult   │  │                │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   基础设施层 (Infrastructure)                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  依赖注入容器    │  │   配置管理       │  │   日志系统       │ │
│  │  (Container)    │  │ (ConfigManager) │  │ (LoggerFactory) │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐                    │
│  │   事件系统       │  │   工具类...      │                    │
│  │  (EventBus)     │  │                │                    │
│  └─────────────────┘  └─────────────────┘                    │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 核心组件

### 1. 依赖注入容器 (Container)

**位置**：`src/core/container.py`

依赖注入容器是整个架构的核心，管理所有服务的生命周期和依赖关系。

```python
from src.core import Container

# 创建容器
container = Container()

# 注册单例服务
container.register_singleton(ConfigManager)
container.register_singleton(LoggerFactory)

# 注册瞬态服务
container.register_transient(DocumentService)

# 解析服务
config_manager = container.resolve(ConfigManager)
```

**支持的生命周期**：
- `SINGLETON`：单例模式，全局唯一实例
- `TRANSIENT`：瞬态模式，每次解析创建新实例
- `SCOPED`：作用域模式，在特定作用域内单例

### 2. 配置管理系统 (ConfigManager)

**位置**：`src/core/config.py`

基于dataclass的类型安全配置系统，支持YAML文件和环境变量。

```python
from src.core import ConfigManager

config_manager = ConfigManager()
config = config_manager.load_config("config/app_config.yaml")

# 访问配置
device = config.ocr_engine.device
model_name = config.layout_detection.model_name
```

**配置结构**：
```python
@dataclass
class AppConfig:
    ocr_engine: OCREngineConfig
    layout_detection: LayoutDetectionConfig
    table_recognition: TableRecognitionConfig
    seal_recognition: SealRecognitionConfig
    ui: UIConfig
    logging: LoggingConfig
```

### 3. 事件系统 (EventBus)

**位置**：`src/core/events.py`

轻量级的发布-订阅事件系统，支持松耦合的组件通信。

```python
from src.core.events import EventBus, ProcessingProgressEvent

# 创建事件总线
event_bus = EventBus()

# 订阅事件
def on_progress(event: ProcessingProgressEvent):
    print(f"Progress: {event.progress}%")

event_bus.subscribe(ProcessingProgressEvent, on_progress)

# 发布事件
event_bus.publish(ProcessingProgressEvent(
    session_id="123",
    step="ocr_processing",
    progress=50.0,
    message="Processing page 2 of 4"
))
```

### 4. 日志系统 (LoggerFactory)

**位置**：`src/core/logging.py`

结构化日志系统，支持多种输出格式和级别。

```python
from src.core.logging import LoggerFactory

logger_factory = LoggerFactory()
logger = logger_factory.get_logger("my_module")

logger.info("Processing started", extra={
    "session_id": "123",
    "file_path": "document.pdf"
})
```

## 🎯 业务服务层

### 1. 文档服务 (DocumentService)

**位置**：`src/services/document_service.py`

负责PDF文档的加载、页面提取和图像预处理。

```python
from src.services import DocumentService

doc_service = DocumentService(config)
document = doc_service.load_document("path/to/file.pdf")

# 提取页面图像
for page_num, image in doc_service.extract_pages_as_images(document):
    print(f"Page {page_num}: {image.shape}")
```

**主要功能**：
- PDF文档加载和验证
- 页面图像提取（支持PyMuPDF和pdf2image）
- 图像预处理（去噪、增强、锐化）
- 临时文件管理

### 2. OCR服务 (OCRService)

**位置**：`src/services/ocr_service.py`

OCR处理的核心编排服务，管理多个OCR引擎的协调工作。

```python
from src.services import OCRService

ocr_service = OCRService(config, event_bus)
ocr_service.initialize_engines()

# 处理文档
result_document = ocr_service.process_document(document, pages_images)
```

**主要功能**：
- OCR引擎初始化和管理
- 并行OCR处理
- 结果整合和验证
- 进度事件发布

### 3. 转换服务 (ConversionService)

**位置**：`src/services/conversion_service.py`

将OCR结果转换为各种输出格式。

```python
from src.services import ConversionService

conv_service = ConversionService(config)
output_path = conv_service.convert_document(document, "xlsx")
```

**支持格式**：
- **Excel (.xlsx/.xls)**：多工作表，格式化表格
- **CSV (.csv)**：纯文本数据，UTF-8编码
- **JSON (.json)**：结构化数据，包含元数据
- **HTML (.html)**：可视化报告，CSS样式

### 4. 进度服务 (ProgressService)

**位置**：`src/services/progress_service.py`

处理会话管理和进度跟踪。

```python
from src.services import ProgressService

progress_service = ProgressService(config, event_bus)
session = progress_service.create_session("session_id", "file.pdf", 10)

# 更新进度
progress_service.update_step_progress("session_id", "ocr_processing", 75.0)
```

## 🤖 OCR引擎层

### PaddleOCR PP-StructureV3 引擎

**位置**：`src/engines/paddleocr/paddle_engine.py`

完整集成PaddleOCR PP-StructureV3的四个核心引擎：

#### 1. 布局检测引擎 (PaddleLayoutEngine)
```python
layout_engine = PaddleLayoutEngine(config)
layout_result = layout_engine.detect_layout(image)
```

**功能**：
- 使用PP-DocLayout-S模型
- 识别文本、表格、图像、标题等区域
- 返回边界框和置信度

#### 2. 文本识别引擎 (PaddleTextEngine)
```python
text_engine = PaddleTextEngine(config)
text_results = text_engine.recognize_text(image, regions)
```

**功能**：
- 使用PP-OCRv5模型
- 支持中英文混合识别
- 高精度文本识别

#### 3. 表格识别引擎 (PaddleTableEngine)
```python
table_engine = PaddleTableEngine(config)
table_results = table_engine.recognize_tables(image, table_regions)
```

**功能**：
- 使用SLANet_plus模型
- 识别表格结构和内容
- 保持行列关系

#### 4. 印章识别引擎 (PaddleSealEngine)
```python
seal_engine = PaddleSealEngine(config)
seal_results = seal_engine.recognize_seals(image, seal_regions)
```

**功能**：
- 专门针对中文印章
- 圆形、方形印章识别
- 高精度文字提取

## 📊 领域模型

### 文档模型

**位置**：`src/domain/models/document.py`

```python
@dataclass
class Document:
    file_path: Path
    pages: List[Page] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_page(self, page: Page) -> None:
        self.pages.append(page)
    
    @property
    def total_pages(self) -> int:
        return len(self.pages)
```

### OCR结果模型

**位置**：`src/domain/models/ocr_result.py`

```python
@dataclass
class OCRResult:
    text_results: List[TextResult]
    table_results: List[TableResult]
    seal_results: List[SealResult]
    layout_result: Optional[LayoutResult]
    processing_time: float
    
    def to_dataframe(self) -> pd.DataFrame:
        # 转换为pandas DataFrame
        pass
```

## 🧪 测试策略

### 单元测试

每个组件都有对应的单元测试：

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定模块测试
python -m pytest tests/test_document_service.py

# 运行覆盖率测试
python -m pytest --cov=src tests/
```

### 集成测试

```bash
# 核心功能测试
python test_core_functionality.py

# 端到端测试
python test_end_to_end.py
```

### 性能测试

```bash
# 性能基准测试
python tests/benchmark_ocr.py

# 内存使用测试
python tests/test_memory_usage.py
```

## 🔄 扩展指南

### 添加新的OCR引擎

1. **创建引擎类**：
```python
# src/engines/new_engine/new_ocr_engine.py
class NewOCREngine(OCREngine):
    def initialize(self) -> None:
        # 初始化引擎
        pass
    
    def process_document(self, document: Document) -> Document:
        # 处理文档
        pass
```

2. **注册引擎**：
```python
# src/services/ocr_service.py
container.register_transient(NewOCREngine)
```

### 添加新的输出格式

1. **扩展转换服务**：
```python
# src/services/conversion_service.py
def convert_to_new_format(self, document: Document) -> Path:
    # 实现新格式转换
    pass
```

2. **更新配置**：
```yaml
# config/app_config.yaml
supported_formats:
  - xlsx
  - csv
  - json
  - html
  - new_format  # 新格式
```

## 📈 性能优化

### 内存优化

1. **使用生成器**：
```python
def extract_pages_as_images(self, document: Document) -> Generator[Tuple[int, np.ndarray], None, None]:
    for page_num in range(document.total_pages):
        yield page_num, self._extract_page_image(page_num)
```

2. **及时清理资源**：
```python
def cleanup_temp_files(self) -> None:
    for temp_file in self.temp_files:
        temp_file.unlink(missing_ok=True)
    self.temp_files.clear()
```

### 并发优化

```python
from concurrent.futures import ThreadPoolExecutor

def process_pages_parallel(self, pages: List[Tuple[int, np.ndarray]]) -> List[OCRResult]:
    with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
        futures = [executor.submit(self._process_page, page) for page in pages]
        return [future.result() for future in futures]
```

## 🐛 调试指南

### 启用调试日志

```python
import logging
logging.getLogger('src').setLevel(logging.DEBUG)
```

### 使用调试工具

```python
# 在代码中添加断点
import pdb; pdb.set_trace()

# 或使用更现代的调试器
import ipdb; ipdb.set_trace()
```

### 性能分析

```python
import cProfile
import pstats

# 性能分析
profiler = cProfile.Profile()
profiler.enable()

# 执行代码
app.process_document(document)

profiler.disable()
stats = pstats.Stats(profiler)
stats.sort_stats('cumulative').print_stats(20)
```

---

**开发团队**：PDF转Excel工具开发组
**最后更新**：2025-09-10
**版本**：v4.0.0
