"""
PDF to Excel Tool - Modern Architecture
======================================

A modern, modular PDF table extraction tool with comprehensive PaddleOCR PP-StructureV3 integration.

This package provides:
- Layout detection using PP-StructureV3
- General OCR pipeline with PP-OCRv5
- Table recognition v2 pipeline
- Seal text recognition pipeline
- Modern dependency injection architecture
- Configurable processing pipelines
"""

__version__ = "4.0.0"
__author__ = "PDF to Excel Tool Team"
__description__ = "Modern PDF table extraction tool with PaddleOCR PP-StructureV3 integration"

# Package metadata
PACKAGE_INFO = {
    "name": "pdf-to-excel-tool",
    "version": __version__,
    "description": __description__,
    "author": __author__,
    "python_requires": ">=3.8",
    "keywords": ["pdf", "excel", "ocr", "table-extraction", "paddleocr"],
}

# Core exports
from .core import Container, ConfigManager, LoggerFactory, get_logger
from .domain.models import Document, Page, Region, BoundingBox
from .services import DocumentService, OCRService, ConversionService, ProgressService
from .app import PDFToExcelApp

__all__ = [
    # Version info
    '__version__',
    '__author__',
    '__description__',

    # Main application
    'PDFToExcelApp',

    # Core components
    'Container',
    'ConfigManager',
    'LoggerFactory',
    'get_logger',

    # Domain models
    'Document',
    'Page',
    'Region',
    'BoundingBox',

    # Services
    'DocumentService',
    'OCRService',
    'ConversionService',
    'ProgressService',
]