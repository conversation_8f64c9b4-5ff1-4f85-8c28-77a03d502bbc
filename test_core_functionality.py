#!/usr/bin/env python3
"""
Core Functionality Test Script
==============================

Test script to verify the refactored PDF to Excel application core functionality.
"""

import sys
import os
from pathlib import Path
import traceback

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all core modules can be imported successfully."""
    print("🔍 Testing imports...")
    
    try:
        # Test core imports
        from src.core import Container, ConfigManager, LoggerFactory, get_logger
        print("✅ Core modules imported successfully")
        
        # Test domain models
        from src.domain.models import Document, Page, Region, BoundingBox
        print("✅ Domain models imported successfully")
        
        # Test services
        from src.services import DocumentService, OCRService, ConversionService, ProgressService
        print("✅ Services imported successfully")
        
        # Test main app
        from src.app import PDFToExcelApp
        print("✅ Main application imported successfully")
        
        # Test engines
        from src.engines.paddleocr import PaddleOCREngine
        from src.engines.camelot import CamelotPDFEngine
        print("✅ OCR engines imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_dependency_injection():
    """Test the dependency injection container."""
    print("\n🔍 Testing dependency injection...")
    
    try:
        from src.core import Container
        
        # Create container
        container = Container()
        
        # Test singleton registration
        class TestService:
            def __init__(self, value: str = "test"):
                self.value = value
        
        container.register_singleton(TestService, factory=lambda: TestService("singleton_test"))
        
        # Test resolution
        service1 = container.resolve(TestService)
        service2 = container.resolve(TestService)
        
        assert service1 is service2, "Singleton services should be the same instance"
        assert service1.value == "singleton_test", "Service should have correct value"
        
        print("✅ Dependency injection container working correctly")
        return True
        
    except Exception as e:
        print(f"❌ DI container test failed: {e}")
        traceback.print_exc()
        return False

def test_configuration():
    """Test configuration management."""
    print("\n🔍 Testing configuration management...")
    
    try:
        from src.core import ConfigManager
        
        # Test default configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # Verify config structure
        assert hasattr(config, 'ocr_engine'), "Config should have ocr_engine"
        assert hasattr(config, 'layout_detection'), "Config should have layout_detection"
        assert hasattr(config, 'table_recognition'), "Config should have table_recognition"
        assert hasattr(config, 'seal_recognition'), "Config should have seal_recognition"
        
        print("✅ Configuration management working correctly")
        print(f"   - OCR Engine Device: {config.ocr_engine.device}")
        print(f"   - Layout Detection Model: {config.layout_detection.model_name}")
        print(f"   - Table Recognition Model: {config.table_recognition.model_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        traceback.print_exc()
        return False

def test_logging():
    """Test logging system."""
    print("\n🔍 Testing logging system...")
    
    try:
        from src.core import LoggerFactory, get_logger
        
        # Configure logging
        LoggerFactory.configure(
            level="INFO",
            enable_console=True,
            enable_file=False,
            structured_logging=False
        )
        
        # Get logger
        logger = get_logger("test_logger")
        
        # Test logging
        logger.info("Test info message")
        logger.warning("Test warning message")
        
        print("✅ Logging system working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Logging test failed: {e}")
        traceback.print_exc()
        return False

def test_domain_models():
    """Test domain models."""
    print("\n🔍 Testing domain models...")
    
    try:
        from src.domain.models import BoundingBox, Region, Page, Document
        from pathlib import Path
        
        # Test BoundingBox
        bbox = BoundingBox(10, 20, 100, 200)
        assert bbox.width == 90, "BoundingBox width calculation incorrect"
        assert bbox.height == 180, "BoundingBox height calculation incorrect"
        assert bbox.area == 16200, "BoundingBox area calculation incorrect"
        
        # Test Region
        region = Region(
            region_id="test_region",
            region_type="text",
            bounding_box=bbox,
            confidence=0.95,
            content="Test content"
        )
        assert region.region_id == "test_region", "Region ID incorrect"
        assert region.confidence == 0.95, "Region confidence incorrect"
        
        # Test Page
        page = Page(page_number=1, width=800, height=1200)
        page.add_region(region)
        assert len(page.regions) == 1, "Page should have one region"
        
        text_regions = page.get_regions_by_type("text")
        assert len(text_regions) == 1, "Page should have one text region"
        
        # Test Document
        document = Document(file_path=Path("test.pdf"))
        document.add_page(page)
        assert document.total_pages == 1, "Document should have one page"
        assert document.total_regions == 1, "Document should have one region"
        
        print("✅ Domain models working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Domain models test failed: {e}")
        traceback.print_exc()
        return False

def test_events():
    """Test event system."""
    print("\n🔍 Testing event system...")
    
    try:
        from src.core.events import publish_event, subscribe_to_event, ProcessingProgressEvent
        
        # Test event subscription and publishing
        received_events = []
        
        def event_handler(event):
            received_events.append(event)
        
        # Subscribe to event
        subscribe_to_event(ProcessingProgressEvent, event_handler)
        
        # Publish event
        test_event = ProcessingProgressEvent(
            current_step="Testing",
            progress_percentage=50.0,
            source="TestScript"
        )
        publish_event(test_event)
        
        # Verify event was received
        assert len(received_events) == 1, "Event should have been received"
        assert received_events[0].current_step == "Testing", "Event data incorrect"
        
        print("✅ Event system working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Event system test failed: {e}")
        traceback.print_exc()
        return False

def test_app_initialization():
    """Test main application initialization."""
    print("\n🔍 Testing application initialization...")
    
    try:
        from src.app import PDFToExcelApp
        from src.services import DocumentService, ConversionService, ProgressService

        # Create app instance
        app = PDFToExcelApp()
        
        # Test initialization (skip OCR engine initialization for testing)
        try:
            app.initialize()
        except RuntimeError as e:
            if "PaddleOCR is not available" in str(e):
                print("   ⚠️  PaddleOCR not installed - testing without OCR engines")
                # Initialize without OCR engines for testing
                app.config = app.config_manager.load_config()
                app._register_services()
                app.document_service = app.container.resolve(DocumentService)
                app.conversion_service = app.container.resolve(ConversionService)
                app.progress_service = app.container.resolve(ProgressService)
                app._initialized = True
            else:
                raise

        assert app.is_initialized, "App should be initialized"
        assert app.document_service is not None, "Document service should be available"
        assert app.conversion_service is not None, "Conversion service should be available"
        assert app.progress_service is not None, "Progress service should be available"
        
        # Test supported formats
        formats = app.get_supported_formats()
        assert 'xlsx' in formats, "Should support Excel format"
        assert 'csv' in formats, "Should support CSV format"
        assert 'json' in formats, "Should support JSON format"
        
        print("✅ Application initialization working correctly")
        print(f"   - Supported formats: {', '.join(formats)}")
        
        # Cleanup
        app.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ Application initialization test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Core Functionality Tests")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_dependency_injection,
        test_configuration,
        test_logging,
        test_domain_models,
        test_events,
        test_app_initialization,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Core functionality is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
