"""
Configuration Management System
===============================

Provides centralized configuration management with support for:
- YAML and JSON configuration files
- Environment variable overrides
- Configuration validation
- Default values
"""

import os
import yaml
import json
from typing import Any, Dict, Optional, Union
from pathlib import Path
from dataclasses import dataclass, field


@dataclass
class OCREngineConfig:
    """OCR engine configuration."""
    use_gpu: bool = False
    device: str = "cpu"
    enable_hpi: bool = False
    use_tensorrt: bool = False
    precision: str = "fp32"
    enable_mkldnn: bool = True
    cpu_threads: int = 8


@dataclass
class LayoutDetectionConfig:
    """Layout detection configuration."""
    model_name: str = "PP-DocLayout-S"
    threshold: float = 0.5
    nms: bool = True
    unclip_ratio: float = 1.0
    merge_bboxes_mode: str = "large"


@dataclass
class TableRecognitionConfig:
    """Table recognition configuration."""
    model_name: str = "SLANet_plus"
    det_limit_side_len: int = 736
    det_limit_type: str = "min"
    det_thresh: float = 0.6
    det_box_thresh: float = 0.6
    det_unclip_ratio: float = 1.6


@dataclass
class SealRecognitionConfig:
    """Seal recognition configuration."""
    det_limit_side_len: int = 736
    det_limit_type: str = "min"
    det_thresh: float = 0.2
    det_box_thresh: float = 0.6
    det_unclip_ratio: float = 0.5
    rec_score_thresh: float = 0.0


@dataclass
class UIConfig:
    """UI configuration."""
    theme: str = "light"
    window_width: int = 800
    window_height: int = 900
    enable_animations: bool = True
    auto_save_settings: bool = True


@dataclass
class AppConfig:
    """Main application configuration."""
    # OCR Engine settings
    ocr_engine: OCREngineConfig = field(default_factory=OCREngineConfig)
    layout_detection: LayoutDetectionConfig = field(default_factory=LayoutDetectionConfig)
    table_recognition: TableRecognitionConfig = field(default_factory=TableRecognitionConfig)
    seal_recognition: SealRecognitionConfig = field(default_factory=SealRecognitionConfig)

    # UI settings
    ui: UIConfig = field(default_factory=UIConfig)

    # General settings
    default_output_format: str = "xlsx"
    max_concurrent_tasks: int = 4
    temp_dir: Optional[str] = None
    log_level: str = "INFO"
    enable_telemetry: bool = False


class ConfigManager:
    """Configuration manager with file loading and environment variable support."""

    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        self.config_path = Path(config_path) if config_path else None
        self._config: Optional[AppConfig] = None

    def load_config(self, config_path: Optional[Union[str, Path]] = None) -> AppConfig:
        """Load configuration from file with environment variable overrides."""
        if config_path:
            self.config_path = Path(config_path)

        # Start with default configuration
        config_dict = self._get_default_config()

        # Load from file if exists
        if self.config_path and self.config_path.exists():
            file_config = self._load_config_file(self.config_path)
            config_dict = self._merge_configs(config_dict, file_config)

        # Apply environment variable overrides
        config_dict = self._apply_env_overrides(config_dict)

        # Create and validate configuration
        self._config = self._create_config_from_dict(config_dict)
        return self._config

    def save_config(self, config: AppConfig, config_path: Optional[Union[str, Path]] = None):
        """Save configuration to file."""
        if config_path:
            self.config_path = Path(config_path)

        if not self.config_path:
            raise ValueError("No config path specified")

        # Ensure directory exists
        self.config_path.parent.mkdir(parents=True, exist_ok=True)

        # Convert config to dictionary
        config_dict = self._config_to_dict(config)

        # Save based on file extension
        if self.config_path.suffix.lower() == '.json':
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
        else:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)

    def get_config(self) -> AppConfig:
        """Get current configuration."""
        if self._config is None:
            self._config = self.load_config()
        return self._config

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration as dictionary."""
        default_config = AppConfig()
        return self._config_to_dict(default_config)

    def _load_config_file(self, config_path: Path) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() == '.json':
                    return json.load(f)
                else:
                    return yaml.safe_load(f) or {}
        except Exception as e:
            raise ValueError(f"Failed to load config file {config_path}: {e}")

    def _merge_configs(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively merge configuration dictionaries."""
        result = base.copy()

        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value

        return result

    def _apply_env_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment variable overrides."""
        # Define environment variable mappings
        env_mappings = {
            'PDF_EXCEL_USE_GPU': ('ocr_engine', 'use_gpu'),
            'PDF_EXCEL_DEVICE': ('ocr_engine', 'device'),
            'PDF_EXCEL_LOG_LEVEL': ('log_level',),
            'PDF_EXCEL_THEME': ('ui', 'theme'),
            'PDF_EXCEL_TEMP_DIR': ('temp_dir',),
        }

        result = config.copy()

        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # Navigate to the nested configuration
                current = result
                for key in config_path[:-1]:
                    if key not in current:
                        current[key] = {}
                    current = current[key]

                # Convert string values to appropriate types
                final_key = config_path[-1]
                if env_value.lower() in ('true', 'false'):
                    current[final_key] = env_value.lower() == 'true'
                elif env_value.isdigit():
                    current[final_key] = int(env_value)
                else:
                    try:
                        current[final_key] = float(env_value)
                    except ValueError:
                        current[final_key] = env_value

        return result

    def _create_config_from_dict(self, config_dict: Dict[str, Any]) -> AppConfig:
        """Create AppConfig instance from dictionary."""
        try:
            # Create nested configuration objects
            ocr_engine = OCREngineConfig(**config_dict.get('ocr_engine', {}))
            layout_detection = LayoutDetectionConfig(**config_dict.get('layout_detection', {}))
            table_recognition = TableRecognitionConfig(**config_dict.get('table_recognition', {}))
            seal_recognition = SealRecognitionConfig(**config_dict.get('seal_recognition', {}))
            ui = UIConfig(**config_dict.get('ui', {}))

            # Create main config
            return AppConfig(
                ocr_engine=ocr_engine,
                layout_detection=layout_detection,
                table_recognition=table_recognition,
                seal_recognition=seal_recognition,
                ui=ui,
                default_output_format=config_dict.get('default_output_format', 'xlsx'),
                max_concurrent_tasks=config_dict.get('max_concurrent_tasks', 4),
                temp_dir=config_dict.get('temp_dir'),
                log_level=config_dict.get('log_level', 'INFO'),
                enable_telemetry=config_dict.get('enable_telemetry', False)
            )
        except Exception as e:
            raise ValueError(f"Failed to create configuration: {e}")

    def _config_to_dict(self, config: AppConfig) -> Dict[str, Any]:
        """Convert AppConfig to dictionary."""
        return {
            'ocr_engine': {
                'use_gpu': config.ocr_engine.use_gpu,
                'device': config.ocr_engine.device,
                'enable_hpi': config.ocr_engine.enable_hpi,
                'use_tensorrt': config.ocr_engine.use_tensorrt,
                'precision': config.ocr_engine.precision,
                'enable_mkldnn': config.ocr_engine.enable_mkldnn,
                'cpu_threads': config.ocr_engine.cpu_threads,
            },
            'layout_detection': {
                'model_name': config.layout_detection.model_name,
                'threshold': config.layout_detection.threshold,
                'nms': config.layout_detection.nms,
                'unclip_ratio': config.layout_detection.unclip_ratio,
                'merge_bboxes_mode': config.layout_detection.merge_bboxes_mode,
            },
            'table_recognition': {
                'model_name': config.table_recognition.model_name,
                'det_limit_side_len': config.table_recognition.det_limit_side_len,
                'det_limit_type': config.table_recognition.det_limit_type,
                'det_thresh': config.table_recognition.det_thresh,
                'det_box_thresh': config.table_recognition.det_box_thresh,
                'det_unclip_ratio': config.table_recognition.det_unclip_ratio,
            },
            'seal_recognition': {
                'det_limit_side_len': config.seal_recognition.det_limit_side_len,
                'det_limit_type': config.seal_recognition.det_limit_type,
                'det_thresh': config.seal_recognition.det_thresh,
                'det_box_thresh': config.seal_recognition.det_box_thresh,
                'det_unclip_ratio': config.seal_recognition.det_unclip_ratio,
                'rec_score_thresh': config.seal_recognition.rec_score_thresh,
            },
            'ui': {
                'theme': config.ui.theme,
                'window_width': config.ui.window_width,
                'window_height': config.ui.window_height,
                'enable_animations': config.ui.enable_animations,
                'auto_save_settings': config.ui.auto_save_settings,
            },
            'default_output_format': config.default_output_format,
            'max_concurrent_tasks': config.max_concurrent_tasks,
            'temp_dir': config.temp_dir,
            'log_level': config.log_level,
            'enable_telemetry': config.enable_telemetry,
        }