"""
Event System
============

Provides a lightweight event bus for decoupled communication between components.
"""

from abc import ABC, abstractmethod
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar
from dataclasses import dataclass
from datetime import datetime
import threading
import weakref
import uuid

T = TypeVar('T', bound='Event')


class Event:
    """Base event class."""

    def __init__(self, event_id: Optional[str] = None, timestamp: Optional[datetime] = None, source: Optional[str] = None):
        self.event_id = event_id or str(uuid.uuid4())
        self.timestamp = timestamp or datetime.now()
        self.source = source


@dataclass
class ProcessingStartedEvent(Event):
    """Event fired when processing starts."""
    file_path: str
    total_pages: int = 0

    def __init__(self, file_path: str, total_pages: int = 0, event_id: Optional[str] = None, timestamp: Optional[datetime] = None, source: Optional[str] = None):
        super().__init__(event_id, timestamp, source)
        self.file_path = file_path
        self.total_pages = total_pages


@dataclass
class ProcessingProgressEvent(Event):
    """Event fired during processing progress."""
    current_step: str
    progress_percentage: float
    details: Optional[str] = None

    def __init__(self, current_step: str, progress_percentage: float, details: Optional[str] = None, event_id: Optional[str] = None, timestamp: Optional[datetime] = None, source: Optional[str] = None):
        super().__init__(event_id, timestamp, source)
        self.current_step = current_step
        self.progress_percentage = progress_percentage
        self.details = details


@dataclass
class ProcessingCompletedEvent(Event):
    """Event fired when processing completes."""
    file_path: str
    output_path: str
    success: bool
    error_message: Optional[str] = None

    def __init__(self, file_path: str, output_path: Optional[str] = None, success: bool = True, error_message: Optional[str] = None, event_id: Optional[str] = None, timestamp: Optional[datetime] = None, source: Optional[str] = None):
        super().__init__(event_id, timestamp, source)
        self.file_path = file_path
        self.output_path = output_path
        self.success = success
        self.error_message = error_message


@dataclass
class OCRResultEvent(Event):
    """Event fired when OCR processing produces results."""
    engine_type: str
    result_data: Any
    processing_time: float

    def __init__(self, engine_type: str, result_data: Any, processing_time: float, event_id: Optional[str] = None, timestamp: Optional[datetime] = None, source: Optional[str] = None):
        super().__init__(event_id, timestamp, source)
        self.engine_type = engine_type
        self.result_data = result_data
        self.processing_time = processing_time


class EventHandler(ABC):
    """Abstract base class for event handlers."""

    @abstractmethod
    def handle(self, event: Event) -> None:
        """Handle an event."""
        pass


class EventBus:
    """Thread-safe event bus for publish-subscribe messaging."""

    def __init__(self):
        self._handlers: Dict[Type[Event], List[weakref.ReferenceType]] = {}
        self._lock = threading.RLock()

    def subscribe(self, event_type: Type[T], handler: Callable[[T], None]) -> None:
        """Subscribe to an event type."""
        with self._lock:
            if event_type not in self._handlers:
                self._handlers[event_type] = []

            # Use weak reference to prevent memory leaks
            weak_handler = weakref.ref(handler)
            self._handlers[event_type].append(weak_handler)

    def subscribe_handler(self, event_type: Type[T], handler: EventHandler) -> None:
        """Subscribe an event handler to an event type."""
        self.subscribe(event_type, handler.handle)

    def unsubscribe(self, event_type: Type[T], handler: Callable[[T], None]) -> None:
        """Unsubscribe from an event type."""
        with self._lock:
            if event_type not in self._handlers:
                return

            # Find and remove the handler
            handlers_to_remove = []
            for weak_handler in self._handlers[event_type]:
                actual_handler = weak_handler()
                if actual_handler is None or actual_handler == handler:
                    handlers_to_remove.append(weak_handler)

            for weak_handler in handlers_to_remove:
                self._handlers[event_type].remove(weak_handler)

            # Clean up empty handler lists
            if not self._handlers[event_type]:
                del self._handlers[event_type]

    def publish(self, event: Event) -> None:
        """Publish an event to all subscribers."""
        with self._lock:
            event_type = type(event)

            # Get handlers for this event type and its base classes
            handlers_to_call = []
            for registered_type, handlers in self._handlers.items():
                if issubclass(event_type, registered_type):
                    handlers_to_call.extend(handlers)

            # Clean up dead references and call handlers
            for weak_handler in handlers_to_call[:]:
                handler = weak_handler()
                if handler is None:
                    # Remove dead reference
                    for handler_list in self._handlers.values():
                        if weak_handler in handler_list:
                            handler_list.remove(weak_handler)
                else:
                    try:
                        handler(event)
                    except Exception as e:
                        # Log error but don't stop other handlers
                        print(f"Error in event handler: {e}")

    def publish_async(self, event: Event) -> None:
        """Publish an event asynchronously."""
        def publish_in_thread():
            self.publish(event)

        thread = threading.Thread(target=publish_in_thread, daemon=True)
        thread.start()

    def clear(self) -> None:
        """Clear all event handlers."""
        with self._lock:
            self._handlers.clear()

    def get_subscriber_count(self, event_type: Type[Event]) -> int:
        """Get the number of subscribers for an event type."""
        with self._lock:
            if event_type not in self._handlers:
                return 0

            # Count live references
            live_count = 0
            for weak_handler in self._handlers[event_type]:
                if weak_handler() is not None:
                    live_count += 1

            return live_count


# Global event bus instance
_global_event_bus: Optional[EventBus] = None


def get_event_bus() -> EventBus:
    """Get the global event bus instance."""
    global _global_event_bus
    if _global_event_bus is None:
        _global_event_bus = EventBus()
    return _global_event_bus


def publish_event(event: Event) -> None:
    """Convenience function to publish an event to the global event bus."""
    get_event_bus().publish(event)


def subscribe_to_event(event_type: Type[T], handler: Callable[[T], None]) -> None:
    """Convenience function to subscribe to an event on the global event bus."""
    get_event_bus().subscribe(event_type, handler)