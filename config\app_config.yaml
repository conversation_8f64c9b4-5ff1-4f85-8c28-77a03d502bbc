# PDF转Excel工具 - 应用程序配置文件
# =============================================

# OCR引擎配置
ocr_engine:
  # 设备类型：cpu 或 gpu
  device: "cpu"
  
  # 是否使用GPU加速（需要CUDA支持）
  use_gpu: false
  
  # CPU线程数（0表示自动检测）
  cpu_threads: 0
  
  # 是否启用高性能推理
  enable_hpi: false
  
  # 是否使用TensorRT加速（GPU模式下）
  use_tensorrt: false
  
  # 推理精度：fp32, fp16, int8
  precision: "fp32"
  
  # 是否启用MKLDNN加速（CPU模式下）
  enable_mkldnn: true
  
  # 支持的语言：ch（中文）, en（英文）, ch_en（中英文混合）
  language: "ch"

# 布局检测配置
layout_detection:
  # 模型名称
  model_name: "PP-DocLayout-S"
  
  # 检测阈值（0.0-1.0）
  threshold: 0.5
  
  # 是否启用非极大值抑制
  enable_nms: true
  
  # 文本框扩展比例
  unclip_ratio: 1.0
  
  # 边界框合并模式：large, small, none
  merge_bboxes_mode: "large"

# 表格识别配置
table_recognition:
  # 模型名称
  model_name: "SLANet_plus"
  
  # 识别阈值（0.0-1.0）
  threshold: 0.5
  
  # 是否合并相邻表格
  merge_tables: true
  
  # 表格最小行数
  min_rows: 2
  
  # 表格最小列数
  min_cols: 2
  
  # 是否保持表格格式
  preserve_format: true

# 印章识别配置
seal_recognition:
  # 是否启用印章识别
  enable: true
  
  # 识别阈值（0.0-1.0）
  threshold: 0.7
  
  # 印章类型：round（圆形）, square（方形）, all（全部）
  seal_types: "all"
  
  # 最小印章尺寸（像素）
  min_size: 50

# 文档处理配置
document_processing:
  # 图像DPI设置
  dpi: 200
  
  # 图像质量（1-100）
  image_quality: 95
  
  # 是否启用图像预处理
  enable_preprocessing: true
  
  # 预处理选项
  preprocessing:
    # 去噪
    denoise: true
    
    # 增强对比度
    enhance_contrast: true
    
    # 锐化
    sharpen: false
    
    # 二值化
    binarize: false

# 并发处理配置
processing:
  # 最大工作线程数（0表示自动检测）
  max_workers: 0
  
  # 每批处理的页面数
  batch_size: 4
  
  # 处理超时时间（秒）
  timeout: 300
  
  # 是否启用并行处理
  enable_parallel: true

# 输出配置
output:
  # 默认输出目录
  default_directory: "output"
  
  # 支持的输出格式
  supported_formats:
    - "xlsx"
    - "xls" 
    - "csv"
    - "json"
    - "html"
  
  # 默认输出格式
  default_format: "xlsx"
  
  # 文件命名模式
  filename_pattern: "{basename}_ocr_{timestamp}"
  
  # 是否覆盖已存在的文件
  overwrite_existing: false

# Excel输出配置
excel_output:
  # 是否创建汇总工作表
  create_summary_sheet: true
  
  # 汇总工作表名称
  summary_sheet_name: "汇总信息"
  
  # 是否包含元数据
  include_metadata: true
  
  # 是否自动调整列宽
  auto_fit_columns: true
  
  # 默认字体
  default_font: "微软雅黑"
  
  # 默认字体大小
  default_font_size: 11

# CSV输出配置
csv_output:
  # 字符编码
  encoding: "utf-8-sig"
  
  # 分隔符
  delimiter: ","
  
  # 引用字符
  quotechar: "\""
  
  # 是否包含表头
  include_header: true

# JSON输出配置
json_output:
  # 是否格式化输出
  pretty_print: true
  
  # 缩进空格数
  indent: 2
  
  # 是否包含置信度信息
  include_confidence: true
  
  # 是否包含坐标信息
  include_coordinates: true

# HTML输出配置
html_output:
  # 是否包含CSS样式
  include_css: true
  
  # 表格样式主题：default, bootstrap, material
  table_theme: "bootstrap"
  
  # 是否启用交互功能
  enable_interactive: true
  
  # 页面标题
  page_title: "PDF转Excel处理结果"

# 内存管理配置
memory_management:
  # 是否启用自动清理
  auto_cleanup: true
  
  # 清理间隔（处理页面数）
  cleanup_interval: 50
  
  # 最大内存使用量（MB，0表示无限制）
  max_memory_mb: 0
  
  # 临时文件保留时间（秒）
  temp_file_ttl: 3600

# 缓存配置
cache:
  # 是否启用缓存
  enable: true
  
  # 缓存目录
  directory: "cache"
  
  # 最大缓存大小（项目数）
  max_size: 1000
  
  # 缓存生存时间（秒）
  ttl: 3600
  
  # 缓存清理间隔（秒）
  cleanup_interval: 1800

# 日志配置
logging:
  # 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
  level: "INFO"
  
  # 日志格式：simple, detailed, json
  format: "detailed"
  
  # 日志输出目标
  handlers:
    # 控制台输出
    console:
      enable: true
      level: "INFO"
      colored: true
    
    # 文件输出
    file:
      enable: true
      level: "DEBUG"
      filename: "logs/app.log"
      max_size_mb: 10
      backup_count: 5
      rotation: "time"  # time 或 size
  
  # 模块日志级别覆盖
  loggers:
    "src.engines.paddleocr": "WARNING"
    "src.services.ocr_service": "INFO"

# 用户界面配置
ui:
  # 界面主题：light, dark, auto
  theme: "auto"
  
  # 界面语言：zh_CN, en_US
  language: "zh_CN"
  
  # 窗口大小
  window_size:
    width: 1200
    height: 800
  
  # 是否记住窗口位置
  remember_position: true
  
  # 进度更新间隔（毫秒）
  progress_update_interval: 100

# 高级配置
advanced:
  # 是否启用调试模式
  debug_mode: false
  
  # 是否启用性能分析
  enable_profiling: false
  
  # 性能分析输出目录
  profiling_output_dir: "profiling"
  
  # 是否启用实验性功能
  enable_experimental: false
  
  # 自定义模型路径（可选）
  custom_model_paths:
    layout_detection: ""
    text_recognition: ""
    table_recognition: ""
    seal_recognition: ""

# 环境变量映射
# 以下配置可以通过环境变量覆盖：
# OCR_DEVICE -> ocr_engine.device
# OCR_USE_GPU -> ocr_engine.use_gpu
# OUTPUT_DIR -> output.default_directory
# LOG_LEVEL -> logging.level
# MAX_WORKERS -> processing.max_workers
