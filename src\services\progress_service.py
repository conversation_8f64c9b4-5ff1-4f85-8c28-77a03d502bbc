"""
Progress Tracking Service
=========================

Service for tracking and managing processing progress across the application.
"""

import time
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import threading

from ..core.logging import get_logger
from ..core.events import (
    subscribe_to_event, ProcessingStartedEvent, ProcessingProgressEvent,
    ProcessingCompletedEvent, OCRResultEvent
)

logger = get_logger(__name__)


class ProcessingStatus(Enum):
    """Processing status enumeration."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ProcessingStep:
    """Represents a single processing step."""
    name: str
    description: str
    progress: float = 0.0
    status: ProcessingStatus = ProcessingStatus.NOT_STARTED
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None

    @property
    def duration(self) -> Optional[float]:
        """Get step duration in seconds."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None

    @property
    def is_completed(self) -> bool:
        """Check if step is completed."""
        return self.status in [ProcessingStatus.COMPLETED, ProcessingStatus.FAILED]


@dataclass
class ProcessingSession:
    """Represents a complete processing session."""
    session_id: str
    file_path: str
    total_pages: int = 0
    current_step: str = ""
    overall_progress: float = 0.0
    status: ProcessingStatus = ProcessingStatus.NOT_STARTED
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    steps: Dict[str, ProcessingStep] = field(default_factory=dict)
    results: Dict[str, Any] = field(default_factory=dict)

    @property
    def duration(self) -> Optional[float]:
        """Get total session duration in seconds."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None

    @property
    def completed_steps(self) -> int:
        """Get number of completed steps."""
        return sum(1 for step in self.steps.values() if step.is_completed)

    @property
    def total_steps(self) -> int:
        """Get total number of steps."""
        return len(self.steps)


class ProgressService:
    """Service for tracking processing progress."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.sessions: Dict[str, ProcessingSession] = {}
        self.progress_callbacks: List[Callable[[ProcessingSession], None]] = []
        self._lock = threading.RLock()

        # Subscribe to events
        self._setup_event_handlers()

    def _setup_event_handlers(self) -> None:
        """Set up event handlers for progress tracking."""
        subscribe_to_event(ProcessingStartedEvent, self._handle_processing_started)
        subscribe_to_event(ProcessingProgressEvent, self._handle_processing_progress)
        subscribe_to_event(ProcessingCompletedEvent, self._handle_processing_completed)
        subscribe_to_event(OCRResultEvent, self._handle_ocr_result)

    def create_session(self, session_id: str, file_path: str, total_pages: int = 0) -> ProcessingSession:
        """Create a new processing session."""
        with self._lock:
            session = ProcessingSession(
                session_id=session_id,
                file_path=file_path,
                total_pages=total_pages,
                start_time=datetime.now()
            )

            # Define standard processing steps
            self._initialize_standard_steps(session)

            self.sessions[session_id] = session
            logger.info(f"Created processing session: {session_id}")
            return session

    def _initialize_standard_steps(self, session: ProcessingSession) -> None:
        """Initialize standard processing steps."""
        standard_steps = [
            ("document_loading", "Loading PDF document"),
            ("page_extraction", "Extracting pages as images"),
            ("ocr_processing", "Performing OCR analysis"),
            ("result_conversion", "Converting results to output format")
        ]

        for step_name, description in standard_steps:
            session.steps[step_name] = ProcessingStep(
                name=step_name,
                description=description
            )

    def update_step_progress(
        self,
        session_id: str,
        step_name: str,
        progress: float,
        status: Optional[ProcessingStatus] = None
    ) -> None:
        """Update progress for a specific step."""
        with self._lock:
            session = self.sessions.get(session_id)
            if not session:
                logger.warning(f"Session not found: {session_id}")
                return

            step = session.steps.get(step_name)
            if not step:
                # Create step if it doesn't exist
                step = ProcessingStep(name=step_name, description=step_name.replace('_', ' ').title())
                session.steps[step_name] = step

            # Update step
            step.progress = min(100.0, max(0.0, progress))

            if status:
                step.status = status
                if status == ProcessingStatus.IN_PROGRESS and not step.start_time:
                    step.start_time = datetime.now()
                elif status in [ProcessingStatus.COMPLETED, ProcessingStatus.FAILED]:
                    step.end_time = datetime.now()

            # Update session overall progress
            self._update_session_progress(session)

            # Notify callbacks
            self._notify_progress_callbacks(session)

    def _update_session_progress(self, session: ProcessingSession) -> None:
        """Update overall session progress based on step progress."""
        if not session.steps:
            return

        total_progress = sum(step.progress for step in session.steps.values())
        session.overall_progress = total_progress / len(session.steps)

        # Update session status
        completed_steps = session.completed_steps
        total_steps = session.total_steps

        if completed_steps == 0:
            session.status = ProcessingStatus.NOT_STARTED
        elif completed_steps == total_steps:
            # Check if any step failed
            failed_steps = [step for step in session.steps.values() if step.status == ProcessingStatus.FAILED]
            if failed_steps:
                session.status = ProcessingStatus.FAILED
            else:
                session.status = ProcessingStatus.COMPLETED
                session.end_time = datetime.now()
        else:
            session.status = ProcessingStatus.IN_PROGRESS

    def _handle_processing_started(self, event: ProcessingStartedEvent) -> None:
        """Handle processing started event."""
        # Find or create session based on file path
        session_id = self._get_session_id_from_file_path(event.file_path)
        if session_id:
            self.update_step_progress(session_id, "document_loading", 100.0, ProcessingStatus.COMPLETED)
            self.update_step_progress(session_id, "page_extraction", 0.0, ProcessingStatus.IN_PROGRESS)

    def _handle_processing_progress(self, event: ProcessingProgressEvent) -> None:
        """Handle processing progress event."""
        # Map event source to step name
        step_mapping = {
            "DocumentService": "page_extraction",
            "OCRService": "ocr_processing",
            "ConversionService": "result_conversion"
        }

        step_name = step_mapping.get(event.source, "unknown_step")

        # Find session (simplified - in practice you'd need better session identification)
        for session in self.sessions.values():
            if session.status == ProcessingStatus.IN_PROGRESS:
                self.update_step_progress(
                    session.session_id,
                    step_name,
                    event.progress_percentage,
                    ProcessingStatus.IN_PROGRESS
                )
                session.current_step = event.current_step
                break

    def _handle_processing_completed(self, event: ProcessingCompletedEvent) -> None:
        """Handle processing completed event."""
        session_id = self._get_session_id_from_file_path(event.file_path)
        if session_id:
            if event.success:
                self.update_step_progress(session_id, "result_conversion", 100.0, ProcessingStatus.COMPLETED)
            else:
                self.mark_session_failed(session_id, event.error_message)

    def _handle_ocr_result(self, event: OCRResultEvent) -> None:
        """Handle OCR result event."""
        # Store OCR results in session
        for session in self.sessions.values():
            if session.status == ProcessingStatus.IN_PROGRESS:
                if 'ocr_results' not in session.results:
                    session.results['ocr_results'] = []
                session.results['ocr_results'].append({
                    'engine': event.engine_type,
                    'processing_time': event.processing_time,
                    'timestamp': event.timestamp
                })
                break

    def _get_session_id_from_file_path(self, file_path: str) -> Optional[str]:
        """Get session ID from file path."""
        for session_id, session in self.sessions.items():
            if session.file_path == file_path:
                return session_id
        return None

    def get_session(self, session_id: str) -> Optional[ProcessingSession]:
        """Get processing session by ID."""
        with self._lock:
            return self.sessions.get(session_id)

    def get_all_sessions(self) -> Dict[str, ProcessingSession]:
        """Get all processing sessions."""
        with self._lock:
            return self.sessions.copy()

    def mark_session_failed(self, session_id: str, error_message: str) -> None:
        """Mark a session as failed."""
        with self._lock:
            session = self.sessions.get(session_id)
            if session:
                session.status = ProcessingStatus.FAILED
                session.end_time = datetime.now()

                # Mark current step as failed
                for step in session.steps.values():
                    if step.status == ProcessingStatus.IN_PROGRESS:
                        step.status = ProcessingStatus.FAILED
                        step.error_message = error_message
                        step.end_time = datetime.now()
                        break

                self._notify_progress_callbacks(session)

    def cancel_session(self, session_id: str) -> None:
        """Cancel a processing session."""
        with self._lock:
            session = self.sessions.get(session_id)
            if session:
                session.status = ProcessingStatus.CANCELLED
                session.end_time = datetime.now()

                # Cancel all in-progress steps
                for step in session.steps.values():
                    if step.status == ProcessingStatus.IN_PROGRESS:
                        step.status = ProcessingStatus.CANCELLED
                        step.end_time = datetime.now()

                self._notify_progress_callbacks(session)

    def add_progress_callback(self, callback: Callable[[ProcessingSession], None]) -> None:
        """Add a progress callback function."""
        self.progress_callbacks.append(callback)

    def remove_progress_callback(self, callback: Callable[[ProcessingSession], None]) -> None:
        """Remove a progress callback function."""
        if callback in self.progress_callbacks:
            self.progress_callbacks.remove(callback)

    def _notify_progress_callbacks(self, session: ProcessingSession) -> None:
        """Notify all progress callbacks."""
        for callback in self.progress_callbacks:
            try:
                callback(session)
            except Exception as e:
                logger.error(f"Error in progress callback: {e}")

    def cleanup_old_sessions(self, max_age_hours: int = 24) -> None:
        """Clean up old completed sessions."""
        with self._lock:
            current_time = datetime.now()
            sessions_to_remove = []

            for session_id, session in self.sessions.items():
                if (session.status in [ProcessingStatus.COMPLETED, ProcessingStatus.FAILED, ProcessingStatus.CANCELLED]
                    and session.end_time):
                    age_hours = (current_time - session.end_time).total_seconds() / 3600
                    if age_hours > max_age_hours:
                        sessions_to_remove.append(session_id)

            for session_id in sessions_to_remove:
                del self.sessions[session_id]
                logger.info(f"Cleaned up old session: {session_id}")

    def get_session_summary(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get a summary of a processing session."""
        session = self.get_session(session_id)
        if not session:
            return None

        return {
            'session_id': session.session_id,
            'file_path': session.file_path,
            'status': session.status.value,
            'overall_progress': session.overall_progress,
            'current_step': session.current_step,
            'total_pages': session.total_pages,
            'duration': session.duration,
            'completed_steps': session.completed_steps,
            'total_steps': session.total_steps,
            'steps': {
                name: {
                    'progress': step.progress,
                    'status': step.status.value,
                    'duration': step.duration
                }
                for name, step in session.steps.items()
            }
        }