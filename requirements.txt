# PDF to Excel Tool Dependencies - Modern Architecture v4.0.0
# ================================================================

# Core Python dependencies
pandas>=1.5.0          # Data processing and DataFrame operations
numpy>=1.21.0          # Numerical computing
openpyxl>=3.1.0        # Excel file writing support
PyYAML>=6.0            # YAML configuration file support

# PDF processing dependencies
PyMuPDF>=1.23.0        # PDF document processing (fitz)
pdf2image>=1.16.0      # PDF to image conversion
Pillow>=9.0.0          # Image processing

# Alternative PDF processing (fallback)
PyPDF2>=3.0.0          # Basic PDF processing
camelot-py>=0.10.1     # PDF table extraction (fallback)

# Image processing and OCR dependencies
opencv-python>=4.8.0   # Computer vision and image processing
numpy>=1.21.0          # Array operations (required by OpenCV)

# GUI framework
PySide6>=6.5.0         # Qt for Python (modern UI)

# System and utility dependencies
psutil>=5.9.0          # System monitoring and process management
pathlib>=1.0.1         # Path handling (usually built-in)

# Logging and configuration
colorama>=0.4.6        # Colored terminal output
python-dotenv>=1.0.0   # Environment variable loading

# Development and testing dependencies (optional)
pytest>=7.0.0          # Testing framework
pytest-cov>=4.0.0     # Coverage reporting
black>=23.0.0          # Code formatting
flake8>=6.0.0          # Code linting

# PaddleOCR PP-StructureV3 dependencies
# =====================================
# Install PaddlePaddle first:
# CPU version: pip install paddlepaddle -i https://mirror.baidu.com/pypi/simple
# GPU version: pip install paddlepaddle-gpu -i https://mirror.baidu.com/pypi/simple
#
# Then install PaddleOCR:
# pip install paddleocr>=2.7.0
#
# Uncomment below for automatic installation (may require manual setup):
# paddlepaddle>=2.5.0
# paddleocr>=2.7.0

# Optional GPU acceleration dependencies
# =====================================
# For NVIDIA GPU support:
# - Install CUDA Toolkit 11.2+ or 12.x
# - Install cuDNN 8.x
# - Use paddlepaddle-gpu instead of paddlepaddle
#
# For performance monitoring:
# nvidia-ml-py3>=11.0.0  # NVIDIA GPU monitoring