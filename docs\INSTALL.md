# PDF表格转Excel工具 - 安装指南

本文档提供了详细的安装步骤，特别关注Windows系统上可能遇到的编译问题。

## 系统要求

- **操作系统**: Windows 7/10/11, macOS 10.13+, 或 Linux
- **Python**: 3.7 或更高版本
- **内存**: 建议4GB以上，处理大型PDF文件时可能需要更多
- **存储空间**: 至少200MB可用空间

## 安装Python

如果您尚未安装Python，请按照以下步骤操作：

### Windows

1. 访问 [Python官网](https://www.python.org/downloads/)
2. 下载最新版本的Python安装程序
3. 运行安装程序，勾选"Add Python to PATH"选项
4. 完成安装后，打开命令提示符并输入 `python --version` 验证安装

### macOS

1. 使用Homebrew安装:
   ```
   brew install python
   ```
2. 或访问 [Python官网](https://www.python.org/downloads/) 下载macOS安装程序
3. 验证安装: `python3 --version`

### Linux

使用包管理器安装:

```
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install python3 python3-pip

# Fedora
sudo dnf install python3 python3-pip

# Arch Linux
sudo pacman -S python python-pip
```

## 安装依赖

1. 下载或克隆本项目到本地:
   ```
   git clone https://github.com/username/PDFtoEXCEL.git
   cd PDFtoEXCEL
   ```

2. 安装Python依赖包:
   ```
   pip install -r requirements.txt
   ```

3. 安装Ghostscript (camelot-py的必要依赖):

   #### Windows
   1. 访问 [Ghostscript下载页面](https://www.ghostscript.com/download/gsdnld.html)
   2. 下载并安装适合您Windows版本的安装程序
   3. 确保将Ghostscript的bin目录添加到系统PATH中:
      - 通常位于 `C:\Program Files\gs\gs9.XX\bin`
      - 打开"系统属性" > "高级" > "环境变量"
      - 编辑PATH变量，添加Ghostscript的bin目录路径

   #### macOS
   ```
   brew install ghostscript
   ```

   #### Linux
   ```
   # Ubuntu/Debian
   sudo apt-get install ghostscript

   # Fedora
   sudo dnf install ghostscript

   # Arch Linux
   sudo pacman -S ghostscript
   ```

4. 安装OpenCV (对于复杂表格的处理):
   ```
   pip install opencv-python
   ```

## 安装PaddleOCR (可选)

PaddleOCR提供了扫描文档表格识别功能。为避免编译问题，**强烈建议使用预编译包**。

### 方法1: 使用预编译包（推荐）

```bash
# 从百度镜像源安装PaddlePaddle预编译包
pip install paddlepaddle -i https://mirror.baidu.com/pypi/simple

# 安装PaddleOCR
pip install paddleocr
```

### 方法2: 使用--only-binary选项

如果遇到NumPy或其他包的编译错误，可以使用`--only-binary`选项强制使用预编译的二进制包：

```bash
pip install numpy --only-binary=numpy
pip install paddlepaddle -i https://mirror.baidu.com/pypi/simple
pip install paddleocr
```

## 处理Windows上的NumPy编译问题

在Windows系统上，安装某些Python包时可能会遇到"无法找到Visual C++ 14.0或更高版本"的错误。这是因为这些包需要编译C/C++代码，而Windows默认没有安装C/C++编译器。

### 解决方案1: 安装Visual Studio构建工具（推荐）

1. 下载并安装[Visual Studio构建工具](https://visualstudio.microsoft.com/visual-cpp-build-tools/)
2. 在安装过程中，选择"C++构建工具"工作负载
3. 安装完成后，重新运行pip安装命令

### 解决方案2: 使用预编译的wheel包

对于NumPy，可以从[Unofficial Windows Binaries](https://www.lfd.uci.edu/~gohlke/pythonlibs/#numpy)下载对应Python版本的.whl文件：

```bash
# 下载后安装
pip install numpy-1.23.5+mkl-cp310-cp310-win_amd64.whl
```

### 解决方案3: 使用Anaconda/Miniconda

Anaconda/Miniconda提供了预编译的科学计算包，可以避免编译问题：

1. 安装[Anaconda](https://www.anaconda.com/products/individual)或[Miniconda](https://docs.conda.io/en/latest/miniconda.html)
2. 创建新环境并安装依赖：
   ```bash
   conda create -n pdfexcel python=3.9
   conda activate pdfexcel
   pip install -r requirements.txt
   conda install -c conda-forge paddlepaddle
   pip install paddleocr
   ```

## GPU加速支持（可选）

如果需要GPU加速，请安装对应CUDA版本的PaddlePaddle：

```bash
# 对于CUDA 11.2
pip install paddlepaddle-gpu==2.5.0.post112 -f https://www.paddlepaddle.org.cn/whl/windows/mkl/avx/stable.html
```

更多GPU支持选项请参考[PaddlePaddle官方安装指南](https://www.paddlepaddle.org.cn/install/quick)

## 验证安装

运行以下命令检查所有依赖是否正确安装:

```python
python -c "import sys, PySide6, pandas, camelot, psutil, numpy, cv2; print('所有依赖已正确安装!')"
```

如果没有显示错误信息，则表示安装成功。

## 启动应用

在项目目录中运行:

```
python pdf_to_excel_gui.py
```

## 常见问题排查

### 问题1: ImportError: DLL load failed

这通常是因为缺少某些系统DLL文件。解决方法：
- 安装最新的[Visual C++ Redistributable](https://support.microsoft.com/en-us/help/2977003/the-latest-supported-visual-c-downloads)

### 问题2: 找不到paddleocr模块

确认安装顺序：
1. 先安装paddlepaddle
2. 再安装paddleocr

### 问题3: 内存错误

处理大型PDF文件时可能需要更多内存。尝试：
- 关闭其他内存密集型应用
- 增加系统虚拟内存
- 升级计算机物理内存

## 联系支持

如果您在安装过程中遇到任何问题，请提交Issue或联系开发团队。 